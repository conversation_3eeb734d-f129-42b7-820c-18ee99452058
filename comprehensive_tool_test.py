#!/usr/bin/env python3
"""
Comprehensive test script to validate all 60+ tools across 7 categories.
"""

import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from agent import Agent
from models import ModelManager
from conversation import ConversationManager

def test_all_tools():
    """Test all tool categories comprehensively."""
    print("🚀 COMPREHENSIVE AI AGENT TOOL VALIDATION")
    print("=" * 60)
    
    try:
        # Initialize required managers
        model_manager = ModelManager()
        conversation_manager = ConversationManager(history_dir=Path.cwd() / "conversations")
        
        # Initialize the agent
        agent = Agent(
            model_manager=model_manager,
            conversation_manager=conversation_manager,
            workspace_dir=Path.cwd()
        )
        print("✅ Agent initialized successfully")
        print(f"📊 Total tools available: {len(agent.tools)}")
        
        # Test results tracking
        results = {
            "passed": 0,
            "failed": 0,
            "categories": {}
        }
        
        # 1. FILE SYSTEM TOOLS
        print("\n📁 TESTING FILE SYSTEM TOOLS")
        print("-" * 40)
        file_tests = [
            ("read", "read agent.py"),
            ("list", "list ."),
            ("search", "search . *.py"),
            ("create_file", "create_file test_temp.txt 'Hello World'"),
            ("delete", "delete test_temp.txt")
        ]
        
        results["categories"]["file"] = test_tool_category(agent, "file", file_tests)
        
        # 2. SHELL/TERMINAL TOOLS
        print("\n💻 TESTING SHELL/TERMINAL TOOLS")
        print("-" * 40)
        shell_tests = [
            ("run_in_terminal", "run_in_terminal echo 'Hello'"),
            ("get_terminal_output", "get_terminal_output"),
            ("background_process", "background_process echo 'Background'")
        ]
        
        results["categories"]["shell"] = test_tool_category(agent, "shell", shell_tests)
        
        # 3. TESTING/DEBUGGING TOOLS
        print("\n🔍 TESTING DEBUGGING TOOLS")
        print("-" * 40)
        testing_tests = [
            ("test_search", "test_search . pytest"),
            ("autonomous_debugger", "autonomous_debugger agent.py"),
            ("lint_check", "lint_check agent.py")
        ]
        
        results["categories"]["testing"] = test_tool_category(agent, "testing", testing_tests)
        
        # 4. WEB & SEARCH TOOLS
        print("\n🌐 TESTING WEB & SEARCH TOOLS")
        print("-" * 40)
        web_tests = [
            ("search", "search python tutorial"),
            ("fetch_webpage", "fetch_webpage https://python.org"),
            ("semantic_web_search", "semantic_web_search machine learning")
        ]
        
        results["categories"]["web"] = test_tool_category(agent, "web", web_tests)
        
        # 5. WORKFLOW & SMART AGENT TOOLS
        print("\n⚡ TESTING WORKFLOW TOOLS")
        print("-" * 40)
        workflow_tests = [
            ("create_new_workspace", "create_new_workspace test_project python true"),
            ("plan_next_step", "plan_next_step 'Starting project' 'Build app'"),
            ("context_compression", "context_compression 'Long text here' 0.5")
        ]
        
        results["categories"]["workflow"] = test_tool_category(agent, "workflow", workflow_tests)
        
        # 6. AI REASONING & NLP TOOLS
        print("\n🧠 TESTING AI REASONING TOOLS")
        print("-" * 40)
        ai_tests = [
            ("intent_recognition", "intent_recognition 'I want to create a function'"),
            ("natural_language_to_code", "natural_language_to_code 'create a hello world function'"),
            ("code_explanation", "code_explanation 'def hello(): print(\"Hello\")'")
        ]
        
        results["categories"]["ai_reasoning"] = test_tool_category(agent, "ai_reasoning", ai_tests)
        
        # 7. ADVANCED EXECUTION FEATURES
        print("\n🚀 TESTING ADVANCED EXECUTION")
        print("-" * 40)
        advanced_tests = [
            ("result_analysis", "result_analysis test_id comprehensive"),
            ("iterative_planning", "iterative_planning 'Build web app' 5"),
            ("context_aware_auto_refactoring", "context_aware_auto_refactoring 'def test(): pass' python")
        ]
        
        results["categories"]["advanced_execution"] = test_tool_category(agent, "advanced_execution", advanced_tests)
        
        # Calculate totals
        for category_results in results["categories"].values():
            results["passed"] += category_results["passed"]
            results["failed"] += category_results["failed"]
        
        # Print final results
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS")
        print("=" * 60)
        print(f"✅ Passed: {results['passed']}")
        print(f"❌ Failed: {results['failed']}")
        print(f"📈 Success Rate: {results['passed']/(results['passed']+results['failed'])*100:.1f}%")
        
        for category, category_results in results["categories"].items():
            print(f"  {category}: {category_results['passed']}/{category_results['passed']+category_results['failed']}")
        
        return results
        
    except Exception as e:
        print(f"❌ Critical error in comprehensive test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_tool_category(agent, tool_name, tests):
    """Test a specific tool category."""
    results = {"passed": 0, "failed": 0, "details": []}
    
    for test_name, test_args in tests:
        try:
            if tool_name == "file":
                result = agent._execute_file(test_args)
            elif tool_name == "shell":
                result = agent._execute_shell(test_args)
            elif tool_name == "testing":
                result = agent._execute_test(test_args)
            elif tool_name == "web":
                result = agent._execute_web(test_args)
            elif tool_name == "workflow":
                result = agent._execute_workflow(test_args)
            elif tool_name == "ai_reasoning":
                result = agent._execute_ai_reasoning(test_args)
            elif tool_name == "advanced_execution":
                result = agent._execute_advanced_execution(test_args)
            else:
                result = "Unknown tool category"
            
            if "Error:" not in result and "error" not in result.lower():
                print(f"  ✅ {test_name}: PASSED")
                results["passed"] += 1
            else:
                print(f"  ❌ {test_name}: FAILED - {result[:100]}...")
                results["failed"] += 1
                
            results["details"].append({
                "test": test_name,
                "args": test_args,
                "result": result[:200],
                "status": "PASSED" if "Error:" not in result else "FAILED"
            })
            
        except Exception as e:
            print(f"  ❌ {test_name}: EXCEPTION - {str(e)[:100]}...")
            results["failed"] += 1
            results["details"].append({
                "test": test_name,
                "args": test_args,
                "result": str(e)[:200],
                "status": "EXCEPTION"
            })
    
    return results

if __name__ == "__main__":
    test_all_tools()
