ye sab tools ho isme sabke logics add kro and ai ko context and toolcalling bhi do sabka and ye sabkuch AND REMEMBER YE SAB smartly handle and orcherstrate kre ye  sabko uprgade kro full and and ye just python me hi coding nhi har ek langgauge framewerok me kuch bhi kr ske jaisa user isse bole waisa full isme issse acche se smart advance thiking powerfull ai agent terminal bnao ye itna ye advance ho and capable ho and ye ye abhi aise nhi kr paa rha isme ye capablites add kro jisse ye isme ye 
 
Execute one step at a time - Perform each action individually rather than attempting multiple operations simultaneously 
 
Analyze results thoroughly - After each step, carefully examine and understand the output, errors, or changes that occurred 
 
Plan next step based on analysis - Use the insights from the current step's results to determine the most appropriate next action 
 
Continue iterative process - Repeat this cycle of execute → analyze → plan → execute until the desired outcome is achieved 
 
Validate against user requirements - Ensure each step and the overall progress aligns with the user's original query and expected results 
 
Provide clear status updates - Communicate what was done, what was learned, and what will be done next at each stage and  FULL STACK PROJECTS BNA SKTA HAI  and isme kuch file bnana predefindend na ho ye khudsabkuch analyse krke khudse create and bnaye chahe dirs folders files ya jo bhi ho isme ye kisi bhi lang ya tehc me  FILES READS WRITE EDIT REMOVE YA JO BHI PRECENT CODEBASE YA PWD ME SAB KR SKE YE BHI FULL POWERFULL AND JAISE USKA SANDBOX TERMINAL ME COMMAND EXECUTE KRNA NEED HONE PE LIKE INSTALL SOMTHING LS DIR YA OTHER JITNA BHI KRNA HO KR SKE YE EK NEW TERMINAL OPEN KRKE USME AND MORE BAAKI BHI AISE SAB TOOLS FEAUTRES DAALO ISME AND YE KuCH BNAYE TOH PHELE HI YE NEXT STEP CALCUTALE KR LE KI ISSE KYA AAGE HO SKTA HAI Predictive Prefetching: Agent background mein user ke next move ko predict kar ke suggestions ready rakhta hai.User Input Layer: User se natural language input liya jata hai (e.g., "Create a new login page").Execution Flow (Parallel Tasks): Multi-threading ke through agent tasks ko parallel execute karta hai for fast results. 
 
. Context-Aware Auto-Refactoring Engine: 
Goal: Code ko refactor karte waqt context ko samajhna aur relevant improvements dena. 
 
Techniques: 
 
Function Extraction: Large functions ko small reusable functions mein refactor karna. 
 
Code Duplication Removal: Repeated code ko identify karna aur DRY (Don't Repeat Yourself) principle ko implement karna. 
 
Modularization: Code ko modules mein split karna chote chote chunks me code likhna and jo code likha usse agee likhe old progress track krke  for better maintainability.Multi-Threaded Agent Execution 
🔹 Parallel Tasks Handling: issme advance file handling grep codes files fucnitions, terminal use ,execute commands on terminal ,search web for info retrival coding features tools and so many other capablites and more tools for fs web ,fetch url,web search coding tool codebase indexing command executions terminal browser and so on tools ho isme  advance coding+file handlnig+ terminal+ others tools or isme full ye sab dalke ek advance workflow ke sath jisse chats ka context natural prompt leke usse aage jo krna hai fir kre user ke kehene pe and ye full advance ho isse pta ho isne kya kr diya kya kr rha and kya aage krna hai kitna complete ho gya sab and baaki sab bhi full and har cheez baat chat ka context bhi ho isse . AI-Powered Code Assistance Natural Language Processing: Understand and execute user commands described in natural language.🛠️ File System Tools
create_file – Create a file with content

edit_file / edit_single_or_multi_file – Insert, delete, modify lines or sections

create_directory – Create nested folders/directories

read_file – Read specific lines or full content

insert_edit_into_file – Insert code/content at specific location

file_search – Search files by glob patterns

grep_search – Regex/text search inside files

list_dir – List files/folders in a directory

list_code_usages – Find where symbols (functions/classes) are used

get_changed_files – Show Git diffs of modified files

get_errors – Fetch lint, syntax, or compiler errors

semantic_search – Natural language search across codebase

get_project_setup_info – Detect framework, language, tooling, etc.

🧪 Testing / Debugging Tools
test_search – Find tests related to source files

test_failure – Capture and analyze test failure messages

run_tests – Automatically execute test suites

autonomous_debugger – Trace, identify and fix bugs automatically

lint_check – Run lint/statically analyze code

self_repair – AI-suggested code fixes based on errors

code_linting_static_analysis – Combine all error & code checkers

💬 Terminal + Shell Tools
run_in_terminal – Run shell commands (Linux/Mac/Windows support)

get_terminal_output – Capture and analyze output from command

get_terminal_last_command – What was the last command run

get_terminal_selection – Get selection from active terminal

get_task_output – Get log from running build/dev task

create_and_run_task – Define and execute terminal tasks

install_python_packages – pip install packages dynamically

configure_python_environment – Setup and manage venv/conda

🌐 Web & Search Tools
fetch_webpage – Scrape webpage content

open_simple_browser – Open webpage in editor/browser

github_repo – Search GitHub repos/snippets


semantic_web_search – Natural lang. Google/Bing/Web search

get_search_view_results – Simulate VS Code search panel

retrieval_augmented_generation (RAG) – Extract + summarize content from multiple sources

🔁 Workflow + Smart Agent Tools
create_new_workspace – Setup a complete dev workspace

run_vscode_command – Simulate VS Code actions

plan_next_step – Decide next action based on result

multi_step_loop – Code → Run → Fix → Test → Refactor

context_aware_refactor – Smart restructuring of code

modularization – Split code into logical modules

code_duplication_removal – Remove repeated code

function_extraction – Extract functions for reuse

code_optimizer – Optimize runtime or structure

multi_language_translator – Convert between languages (e.g., Python → Go)

context_compression – Trim large prompts for performance

context_tracking_memory – Track what user said, did, expects

task_memory_status – Show what % of task is done

smart_status_report – Status + what’s next

🧠 AI + Reasoning + NLP Tools
natural_language_to_code – Turn plain English to code

intent_recognition – Understand what user wants

self_critique – Evaluate own answers, improve results

chain_of_thought_reasoning – Break down complex steps

smart_prefetching – Predict next user intent/code and prepare

background_task_prediction – Prepare for next step silently

predict_next_code_block – Anticipate what comes next

auto_complete – Smart suggestions while writing

next_step_prediction – Proactive next-task suggestions

auto_tool_invocation – Run tools without manual command

natural_input_handler – Take human-style chat and route it

interactive_loop – Never end CLI input mode

contextual_autocomplete – Adaptive autocomplete by current state

analyze_context – AST or semantic parser with TreeSitter/Chevrotain