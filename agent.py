"""
Agent module for the Advanced AI Agent.
"""

import re
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Generator, Callable

from models import ModelManager
from conversation import Conversation, Message, ConversationManager
from tools import (
    ShellTool, FileTool, CodeTool, WebTool, CodebaseTool, VisionTool,
    TmuxTool, PatchTool, BrowserTool, RagTool, SearchAPI,
    WebScraperTool, InformationSynthesizer, WebInfoManager, TestingTool, WorkflowTool, AIReasoningTool, AdvancedExecutionTool
)
from core.ai_code_assistant import AICodeAssistant, AssistantRequest, AssistantResponse
from core.iterative_enhanced_agent import IterativeEnhancedAgent
from utils import get_logger

# Get the logger
logger = get_logger()

class Agent:
    """The AI agent."""

    def __init__(
        self,
        model_manager: ModelManager,
        conversation_manager: ConversationManager,
        workspace_dir: Optional[Path] = None,
        system_prompt: Optional[str] = None
    ):
        """Initialize the agent.

        Args:
            model_manager: The model manager to use.
            conversation_manager: The conversation manager to use.
            workspace_dir: The workspace directory to use. If None, will use the current directory.
            system_prompt: The system prompt to use. If None, will use a default prompt.
        """
        self.model_manager = model_manager
        self.conversation_manager = conversation_manager
        self.workspace_dir = workspace_dir or Path.cwd()
        self._has_rag = False  # Initialize _has_rag attribute

        # Initialize tools first, then set the system prompt
        self._initialize_tools()

        # Initialize AI Code Assistant
        self.ai_code_assistant = AICodeAssistant(self.model_manager, self.workspace_dir)

        # Initialize Iterative Enhanced Agent for advanced capabilities
        try:
            self.iterative_agent = IterativeEnhancedAgent(
                self.model_manager,
                self.conversation_manager,
                self.workspace_dir
            )
            self._has_iterative_agent = True
            logger.info("Iterative enhanced agent initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize iterative enhanced agent: {e}")
            self.iterative_agent = None
            self._has_iterative_agent = False

        # Set the system prompt
        if system_prompt is None:
            self.system_prompt = self._get_default_system_prompt()
        else:
            self.system_prompt = system_prompt

    def _initialize_tools(self):
        """Initialize the tools."""
        # Initialize tools
        self.shell_tool = ShellTool(self.workspace_dir)
        self.file_tool = FileTool(self.workspace_dir)
        self.code_tool = CodeTool()
        self.web_tool = WebTool()
        self.codebase_tool = CodebaseTool(self.workspace_dir)
        self.vision_tool = VisionTool(self.workspace_dir)
        self.tmux_tool = TmuxTool()
        self.patch_tool = PatchTool(self.workspace_dir)
        self.browser_tool = BrowserTool()
        self.search_api = SearchAPI()

        # Initialize new advanced web tools
        self.web_scraper = WebScraperTool()
        self.info_synthesizer = InformationSynthesizer()
        self.web_info_manager = WebInfoManager()

        # Initialize testing and debugging tool
        self.testing_tool = TestingTool(self.workspace_dir)

        # Initialize workflow and smart agent tool
        self.workflow_tool = WorkflowTool(self.workspace_dir)

        # Initialize AI reasoning and NLP tool
        self.ai_reasoning_tool = AIReasoningTool(self.workspace_dir)

        # Initialize advanced execution tool
        self.advanced_execution_tool = AdvancedExecutionTool(self.workspace_dir)

        # Initialize RAG tool if dependencies are available
        try:
            # Check if the required modules are available
            import importlib.util
            has_faiss = importlib.util.find_spec("faiss") is not None
            has_sentence_transformers = importlib.util.find_spec("sentence_transformers") is not None

            if has_faiss and has_sentence_transformers:
                self.rag_tool = RagTool(self.workspace_dir)
                self._has_rag = True
            else:
                self._has_rag = False
        except Exception:
            self._has_rag = False

        # Tool registry
        self.tools = {
            "shell": self._execute_shell,
            "file": self._execute_file,
            "code": self._execute_code,
            "web": self._execute_web,
            "codebase": self._execute_codebase,
            "vision": self._execute_vision,
            "tmux": self._execute_tmux,
            "patch": self._execute_patch,
            "browser": self._execute_browser,
            "search": self._execute_search,
            "scrape": self._execute_scrape,
            "info": self._execute_info,
            "ai_assistant": self._execute_ai_assistant,
            "test": self._execute_test,
            "workflow": self._execute_workflow,
            "ai": self._execute_ai_reasoning,
            "execute": self._execute_advanced_execution,
        }

        # Add RAG tool if available
        if self._has_rag:
            self.tools["rag"] = self._execute_rag

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt.

        Returns:
            The default system prompt.
        """
        tools_list = """
1. shell - Execute shell commands
2. file - Advanced file system operations (create, edit, search, analyze)
3. code - Execute code in various programming languages
4. web - Search the web and fetch URLs
5. codebase - Analyze and search code
6. vision - Process images and take screenshots
7. tmux - Manage terminal sessions
8. patch - Apply patches to files
9. browser - Browse the web and take screenshots
10. search - Reliable web search with multiple fallback methods
11. scrape - Advanced web scraping with multiple fallback mechanisms
12. info - Process and synthesize information from multiple sources
13. ai_assistant - Advanced AI code analysis, generation, and optimization
14. test - Comprehensive testing and debugging tools

🛠️ Advanced File System Operations:
- create_file: Create files with content
- edit_file: Insert, delete, modify lines or sections
- create_directory: Create nested folders/directories
- read: Read specific lines or full content
- insert_edit_into_file: Insert code/content at specific location
- file_search: Search files by glob patterns, regex, or name
- grep: Regex/text search inside files
- list: List files/folders in directories
- list_code_usages: Find where symbols (functions/classes) are used
- get_changed_files: Show Git diffs of modified files
- get_errors: Fetch lint, syntax, or compiler errors
- semantic_search: Natural language search across codebase
- get_project_setup_info: Detect framework, language, tooling, etc.

🧪 Testing & Debugging Operations:
- test_search: Find tests related to source files
- test_failure: Capture and analyze test failure messages
- run_tests: Automatically execute test suites
- autonomous_debugger: Trace, identify and fix bugs automatically
- lint_check: Run lint/static analysis on code
- self_repair: AI-suggested code fixes based on errors
- code_linting_static_analysis: Combine all error & code checkers

🖥️ Terminal/Shell Operations:
- run_in_terminal: Run shell commands with Linux/Mac/Windows support
- get_terminal_output: Capture and analyze output from recent commands
- get_terminal_last_command: Get the last command that was run
- get_terminal_selection: Get selection from active terminal
- get_task_output: Get log from running build/dev task
- create_and_run_task: Define and execute terminal tasks
- install_python_packages: Install Python packages dynamically
- configure_python_environment: Setup and manage Python virtual environments

🌐 Web & Search Operations:
- search: Basic web search
- fetch: Fetch URL content
- fetch_webpage: Fetch and parse webpage with advanced extraction
- open_simple_browser: Open URL in simple browser simulation
- github_repo: Access GitHub repositories and extract information
- semantic_web_search: Intelligent web search with semantic understanding
- get_search_view_results: Get comprehensive search results with content extraction
- retrieval_augmented_generation: Implement RAG for web content

⚡ Workflow & Smart Agent Operations:
- create_new_workspace: Create and setup new project workspace
- run_vscode_command: Execute VSCode commands and extensions
- plan_next_step: AI-powered planning for next development steps
- multi_step_loop: Execute multi-step workflows with progress tracking
- context_aware_refactor: Smart code refactoring with context awareness
- context_compression: Compress context while preserving important information
- context_tracking_memory: Track and manage context memory across sessions

🧠 AI Reasoning & NLP Operations:
- natural_language_to_code: Convert natural language descriptions to code
- intent_recognition: Recognize intent from natural language text
- self_critique: Perform self-critique and improvement suggestions on code
- chain_of_thought_reasoning: Implement chain-of-thought reasoning for complex problems
- smart_prefetching: Predict and prefetch likely needed resources/data
- background_task_prediction: Predict background tasks that should be running
- predict_next_code_block: Predict what code block should come next
- auto_complete: Provide intelligent auto-completion suggestions
- next_step_prediction: Predict the next logical step in a workflow

🚀 Advanced Execution Features:
- step_by_step_execution: Execute plans step by step with detailed tracking
- result_analysis: Analyze execution results with detailed insights
- iterative_planning: Implement iterative planning with adaptive refinement
- multi_threading_execution: Execute tasks using multi-threading for parallel processing
- context_aware_auto_refactoring: Perform context-aware automatic refactoring"""

        # Add RAG tool if available
        if self._has_rag:
            tools_list += """
10. rag - Retrieval-augmented generation"""

        # Define examples
        rag_examples = ""
        if self._has_rag:
            rag_examples = """

To use RAG:
```rag
search "How does React work?"
```"""

        return f"""
You are caren an advanced AI coding agent that can help with various tasks.
You have access to the following tools:
{tools_list}

When you need to use a tool, use the following format:
```tool_name
command or arguments
```

For example, to execute a shell command:
```shell
Get-ChildItem -Force
```

Advanced File Operations:
```file
read file.txt
```

```file
create_file new_file.py "print('Hello World')"
```

```file
create_directory src/components
```

```file
edit_file main.py replace 10 "new line content"
```

```file
insert_edit_into_file app.py "import os" 1 0
```

```file
file_search "*.py" glob true
```

```file
list_code_usages "function_name"
```

```file
get_changed_files HEAD~1
```

```file
get_errors main.py
```

```file
semantic_search "authentication logic"
```

```file
get_project_setup_info
```

To execute code in a specific language:
```code
python
print("Hello, world!")
```

```code
javascript
console.log("Hello, world!");
```

To search the web:
```web
search how to use React hooks
```

To analyze code:
```codebase
analyze main.py
```

To take a screenshot:
```vision
take_screenshot
```

To create a tmux session:
```tmux
new_session powershell
```

To apply a patch:
```patch
apply_patch file.py "original code" "updated code"
```

To browse a website:
```browser
read_url https://example.com
```

To search the web with the reliable search tool:
```search
python programming best practices
```

To scrape content from a website with advanced fallback mechanisms:
```scrape
https://example.com
```

To retrieve and synthesize information about a topic:
```info
retrieve artificial intelligence
```

To extract key information from text:
```info
extract "This is a long text that contains important information about various topics..."
```

To format information in a structured manner:
```info
format "Information to format" markdown
```

Testing and Debugging Operations:
```test
test_search main.py
```

```test
run_tests "test_*.py"
```

```test
autonomous_debugger app.py
```

```test
lint_check src/
```

```test
self_repair buggy_file.py
```

```test
code_linting_static_analysis
```

Terminal/Shell Operations:
```shell
run_in_terminal "npm start" bash true
```

```shell
get_terminal_output 100
```

```shell
create_and_run_task "dev_server" "python manage.py runserver" true
```

```shell
install_python_packages "requests,numpy" true true
```

```shell
configure_python_environment "myproject" "3.9" "venv" true
```

Web & Search Operations:
```web
fetch_webpage "https://example.com" true true false
```

```web
open_simple_browser "https://github.com" 5 false
```

```web
github_repo "https://github.com/user/repo" "info"
```

```web
semantic_web_search "machine learning" "academic" 10
```

```web
get_search_view_results "Python tutorials" 3 true
```

```web
retrieval_augmented_generation "What is AI?" "https://example.com,https://another.com" 2000
```

Workflow & Smart Agent Operations:
```workflow
create_new_workspace "my_project" "python" true
```

```workflow
plan_next_step "Building a web app with Python Flask" "Create a complete REST API" "setup_environment,install_flask"
```

```workflow
multi_step_loop "abc12345" true
```

```workflow
context_aware_refactor "main.py" "performance"
```

```workflow
context_compression "Large context data here..." 500 "summary"
```

```workflow
context_tracking_memory "project_context" store '{"language": "python", "framework": "flask"}'
```

AI Reasoning & NLP Operations:
```ai
natural_language_to_code "create a function that calculates fibonacci numbers" python
```

```ai
intent_recognition "I want to fix the bug in my authentication system"
```

```ai
self_critique "def calculate(x, y): return x + y" python "readability,performance"
```

```ai
chain_of_thought_reasoning "How to optimize database queries in my web application"
```

```ai
smart_prefetching "Currently debugging authentication issues in Flask app" 5
```

```ai
predict_next_code_block "def fibonacci(n):\n    if n <= 1:" python
```

```ai
auto_complete "import os" python 5
```

```ai
next_step_prediction "Set up Flask project structure" "Build a complete web application"
```

Advanced Execution Features:
```execute
step_by_step_execution '{"steps": [{"id": "step1", "description": "Setup", "command": "shell:mkdir project", "dependencies": []}]}' false
```

```execute
result_analysis "abc12345" "comprehensive"
```

```execute
iterative_planning "Build a REST API with authentication" 5 0.7
```

```execute
multi_threading_execution '[{"id": "task1", "command": "test_function"}, {"id": "task2", "command": "build_project"}]' 4
```

```execute
context_aware_auto_refactoring "main.py,utils.py" "readability,performance"
```{rag_examples}

Always provide clear explanations of what you're doing and why.
If you're not sure about something, ask for clarification.
Be helpful, accurate, and concise.
"""

    def _prepare_conversation_history(self, conversation: Conversation) -> List[Dict[str, Any]]:
        """Prepare conversation history for the model.

        Args:
            conversation: The conversation to prepare history from.

        Returns:
            List of message dictionaries for the model.
        """
        # Convert conversation messages to the format expected by the model
        history = []

        # Add system prompt as the first message if not already in conversation
        if not any(msg.role == "system" for msg in conversation.messages):
            history.append({
                "role": "system",
                "content": self.system_prompt
            })

        # Add all messages from the conversation
        for message in conversation.messages:
            # Skip tool messages for now as they're handled separately
            if message.role not in ["tool", "tool_result"]:
                history.append({
                    "role": message.role,
                    "content": message.content
                })

        return history

    def process_message(self, message: str, conversation: Optional[Conversation] = None, use_iterative_agent: bool = False) -> str:
        """Process a message from the user.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to. If None, will use the current conversation.
            use_iterative_agent: Whether to use the iterative enhanced agent for advanced processing.

        Returns:
            The response from the agent.
        """
        # Check if we should use the iterative enhanced agent
        if use_iterative_agent and self._has_iterative_agent:
            try:
                return self.iterative_agent.process_message(message, conversation, enable_iterative_improvement=True)
            except Exception as e:
                logger.error(f"Error using iterative agent, falling back to standard processing: {e}")

        # Standard processing
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Prepare conversation history
        conversation_history = self._prepare_conversation_history(conversation)

        # Generate the response using conversation history
        response = self.model_manager.generate(
            prompt=message,
            system_prompt=self.system_prompt,
            conversation_history=conversation_history
        )

        # Process the response for tool calls
        processed_response = self._process_response(response, conversation)

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Check if we should summarize the conversation
        if (conversation.get_message_count() >= self.conversation_manager.auto_summarize_threshold and
            not conversation.summary):
            # Try to summarize the conversation
            self.conversation_manager.summarize_conversation(conversation, self.model_manager)

        # Save the conversation
        self.conversation_manager.save_conversation()

        return processed_response

    def stream_process_message(
        self,
        message: str,
        conversation: Optional[Conversation] = None,
        callback: Optional[Callable[[str], None]] = None,
        use_iterative_agent: bool = False
    ) -> Generator[str, None, None]:
        """Process a message from the user and stream the response.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to. If None, will use the current conversation.
            callback: A callback function to call with each chunk of the response.
            use_iterative_agent: Whether to use the iterative enhanced agent for advanced processing.

        Yields:
            Chunks of the response.
        """
        # Check if we should use the iterative enhanced agent
        if use_iterative_agent and self._has_iterative_agent:
            try:
                for chunk in self.iterative_agent.stream_process_message(message, conversation, callback):
                    yield chunk
                return
            except Exception as e:
                logger.error(f"Error using iterative agent for streaming, falling back to standard processing: {e}")

        # Standard streaming processing
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Prepare conversation history
        conversation_history = self._prepare_conversation_history(conversation)

        # Generate the response using conversation history
        response_chunks = []
        for chunk in self.model_manager.stream_generate(
            prompt=message,
            system_prompt=self.system_prompt,
            conversation_history=conversation_history
        ):
            response_chunks.append(chunk)
            if callback:
                callback(chunk)
            yield chunk

        # Process the response for tool calls
        response = "".join(response_chunks)
        processed_response = self._process_response(response, conversation)

        # If the response was processed (tool calls), yield the processed response
        if processed_response != response:
            if callback:
                callback(processed_response)
            yield processed_response

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Check if we should summarize the conversation
        if (conversation.get_message_count() >= self.conversation_manager.auto_summarize_threshold and
            not conversation.summary):
            # Try to summarize the conversation
            self.conversation_manager.summarize_conversation(conversation, self.model_manager)

        # Save the conversation
        self.conversation_manager.save_conversation()

    def _process_response(self, response: str, conversation: Conversation) -> str:
        """Process a response for tool calls.

        Args:
            response: The response to process.
            conversation: The conversation to add tool messages to.

        Returns:
            The processed response.
        """
        # Check for tool calls
        tool_pattern = r"```(\w+)\n(.*?)```"
        tool_matches = re.finditer(tool_pattern, response, re.DOTALL)

        # Process each tool call
        for match in tool_matches:
            tool_name = match.group(1)
            tool_args = match.group(2).strip()

            # Check if the tool exists
            if tool_name in self.tools:
                # Execute the tool
                tool_result = self.tools[tool_name](tool_args)

                # Add the tool call and result to the conversation
                conversation.add_message("tool", f"```{tool_name}\n{tool_args}\n```")
                conversation.add_message("tool_result", tool_result)

                # Replace the tool call with the result in the response
                response = response.replace(match.group(0), f"```{tool_name}\n{tool_args}\n```\n\n{tool_result}")

        return response

    def _execute_shell(self, args: str) -> str:
        """Execute a shell/terminal operation.

        Args:
            args: The arguments for the shell operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            # Default behavior - execute as direct command
            try:
                stdout, stderr, return_code = self.shell_tool.execute(args)

                if return_code == 0:
                    if stdout.strip():
                        return f"Command executed successfully:\n\n{stdout}"
                    else:
                        return "Command executed successfully."
                else:
                    return f"Command failed with return code {return_code}:\n\n{stderr}"

            except Exception as e:
                return f"Error executing command: {e}"

        operation = args_parts[0]

        if operation == "run_in_terminal":
            # Run shell commands with Linux/Mac/Windows support
            if len(args_parts) < 2:
                return "Error: No command specified for run_in_terminal."

            # Parse: command [shell_type] [background]
            cmd_args = args_parts[1].split(maxsplit=2)
            command = cmd_args[0]
            shell_type = cmd_args[1] if len(cmd_args) > 1 else None
            background = cmd_args[2].lower() == "true" if len(cmd_args) > 2 else False

            try:
                result = self.shell_tool.run_in_terminal(command, shell_type, background)
                if result["success"]:
                    return f"Terminal execution result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in terminal execution: {result['error']}"
            except Exception as e:
                return f"Error in terminal execution: {e}"

        elif operation == "get_terminal_output":
            # Capture and analyze output from recent commands
            lines = int(args_parts[1]) if len(args_parts) > 1 else 50

            try:
                result = self.shell_tool.get_terminal_output(lines)
                if result["success"]:
                    return f"Terminal output:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error getting terminal output: {result['error']}"
            except Exception as e:
                return f"Error getting terminal output: {e}"

        elif operation == "get_terminal_last_command":
            # Get the last command that was run
            try:
                result = self.shell_tool.get_terminal_last_command()
                if result["success"]:
                    return f"Last command:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error getting last command: {result['message']}"
            except Exception as e:
                return f"Error getting last command: {e}"

        elif operation == "get_terminal_selection":
            # Get selection from active terminal
            if len(args_parts) < 2:
                return "Error: No line range specified for terminal selection."

            # Parse: start_line end_line
            range_args = args_parts[1].split()
            if len(range_args) < 2:
                return "Error: Both start_line and end_line required."

            try:
                start_line = int(range_args[0])
                end_line = int(range_args[1])
                result = self.shell_tool.get_terminal_selection(start_line, end_line)
                if result["success"]:
                    return f"Terminal selection:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error getting terminal selection: {result['error']}"
            except ValueError:
                return "Error: Invalid line numbers."
            except Exception as e:
                return f"Error getting terminal selection: {e}"

        elif operation == "get_task_output":
            # Get log from running build/dev task
            if len(args_parts) < 2:
                return "Error: No task name specified."

            # Parse: task_name [lines]
            task_args = args_parts[1].split()
            task_name = task_args[0]
            lines = int(task_args[1]) if len(task_args) > 1 else 50

            try:
                result = self.shell_tool.get_task_output(task_name, lines)
                if result["success"]:
                    return f"Task output for '{task_name}':\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error getting task output: {result['error']}"
            except Exception as e:
                return f"Error getting task output: {e}"

        elif operation == "create_and_run_task":
            # Define and execute terminal tasks
            if len(args_parts) < 2:
                return "Error: No task arguments specified."

            # Parse: task_name command [background] [auto_restart]
            task_args = args_parts[1].split(maxsplit=3)
            if len(task_args) < 2:
                return "Error: Both task_name and command required."

            task_name = task_args[0]
            command = task_args[1]
            background = task_args[2].lower() == "true" if len(task_args) > 2 else True
            auto_restart = task_args[3].lower() == "true" if len(task_args) > 3 else False

            try:
                result = self.shell_tool.create_and_run_task(task_name, command, background, auto_restart)
                if result["success"]:
                    return f"Task creation result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error creating task: {result['error']}"
            except Exception as e:
                return f"Error creating task: {e}"

        elif operation == "install_python_packages":
            # Install Python packages dynamically
            if len(args_parts) < 2:
                return "Error: No packages specified."

            # Parse: packages [use_pip] [upgrade] [requirements_file]
            pkg_args = args_parts[1].split()
            packages = pkg_args[0].split(',') if pkg_args else []
            use_pip = pkg_args[1].lower() != "false" if len(pkg_args) > 1 else True
            upgrade = pkg_args[2].lower() == "true" if len(pkg_args) > 2 else False
            requirements_file = pkg_args[3] if len(pkg_args) > 3 else None

            try:
                result = self.shell_tool.install_python_packages(packages, use_pip, upgrade, requirements_file)
                if result["success"]:
                    return f"Package installation result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error installing packages: {result['error']}"
            except Exception as e:
                return f"Error installing packages: {e}"

        elif operation == "configure_python_environment":
            # Setup and manage Python virtual environments
            if len(args_parts) < 2:
                return "Error: No environment name specified."

            # Parse: env_name [python_version] [env_type] [activate]
            env_args = args_parts[1].split()
            env_name = env_args[0]
            python_version = env_args[1] if len(env_args) > 1 else None
            env_type = env_args[2] if len(env_args) > 2 else "venv"
            activate = env_args[3].lower() != "false" if len(env_args) > 3 else True

            try:
                result = self.shell_tool.configure_python_environment(env_name, python_version, env_type, activate)
                if result["success"]:
                    return f"Environment configuration result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error configuring environment: {result['error']}"
            except Exception as e:
                return f"Error configuring environment: {e}"

        else:
            # Default behavior - execute as direct command
            try:
                stdout, stderr, return_code = self.shell_tool.execute(args)

                if return_code == 0:
                    if stdout.strip():
                        return f"Command executed successfully:\n\n{stdout}"
                    else:
                        return "Command executed successfully."
                else:
                    return f"Command failed with return code {return_code}:\n\n{stderr}"

            except Exception as e:
                return f"Error executing command: {e}"

    def _execute_file(self, args: str) -> str:
        """Execute a file operation.

        Args:
            args: The arguments for the file operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No file operation specified."

        operation = args_parts[0]

        if operation == "read":
            # Read a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                content = self.file_tool.read_file(file_path)
                return f"File content:\n\n{content}"
            except Exception as e:
                return f"Error reading file: {e}"

        elif operation == "write":
            # Write a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse the file path and content
            # Use a more robust approach to split only on the first space
            # This ensures file content with spaces is handled correctly
            parts_text = args_parts[1]
            try:
                # Find the first space after the file path
                space_index = parts_text.index(' ')
                file_path = parts_text[:space_index].strip()
                content = parts_text[space_index+1:].strip()
            except ValueError:
                # No space found, treat the whole string as the file path
                return "Error: No file content specified."

            if not file_path:
                return "Error: No file path specified."

            if not content:
                return "Error: No file content specified."

            try:
                # Create parent directories if they don't exist
                path_obj = Path(self.workspace_dir) / file_path
                path_obj.parent.mkdir(parents=True, exist_ok=True)

                # Write the file
                self.file_tool.write_file(file_path, content)
                return f"File written successfully: {file_path}"
            except Exception as e:
                return f"Error writing file: {e}"

        elif operation == "append":
            # Append to a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse the file path and content using a more robust approach
            parts_text = args_parts[1]
            try:
                # Find the first space after the file path
                space_index = parts_text.index(' ')
                file_path = parts_text[:space_index].strip()
                content = parts_text[space_index+1:].strip()
            except ValueError:
                # No space found, treat the whole string as the file path
                return "Error: No file content specified."

            if not file_path:
                return "Error: No file path specified."

            if not content:
                return "Error: No file content specified."

            try:
                # Create parent directories if they don't exist
                path_obj = Path(self.workspace_dir) / file_path
                path_obj.parent.mkdir(parents=True, exist_ok=True)

                # Append to the file
                self.file_tool.append_file(file_path, content)
                return f"Content appended successfully to: {file_path}"
            except Exception as e:
                return f"Error appending to file: {e}"

        elif operation == "delete":
            # Delete a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                self.file_tool.delete_file(file_path)
                return f"File deleted successfully: {file_path}"
            except Exception as e:
                return f"Error deleting file: {e}"

        elif operation == "list":
            # List files in a directory
            directory = args_parts[1] if len(args_parts) > 1 else "."

            try:
                files = self.file_tool.list_files(directory)
                return f"Files in {directory}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error listing files: {e}"

        elif operation == "search":
            # Search for files
            if len(args_parts) < 2:
                return "Error: No search pattern specified."

            pattern = args_parts[1]

            try:
                files = self.file_tool.search_files(pattern)
                return f"Files matching {pattern}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error searching for files: {e}"

        elif operation == "grep":
            # Search for a pattern in files
            if len(args_parts) < 2:
                return "Error: No grep arguments specified."

            # Parse the pattern and file pattern
            grep_parts = args_parts[1].split(maxsplit=1)
            if len(grep_parts) < 2:
                pattern = grep_parts[0]
                file_pattern = "*"
            else:
                pattern = grep_parts[0]
                file_pattern = grep_parts[1]

            try:
                results = self.file_tool.grep_files(pattern, file_pattern)
                return f"Grep results for {pattern} in {file_pattern}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error grepping files: {e}"

        elif operation == "create_file":
            # Create a new file with content
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse file path and content
            parts_text = args_parts[1]
            try:
                space_index = parts_text.index(' ')
                file_path = parts_text[:space_index].strip()
                content = parts_text[space_index+1:].strip()
            except ValueError:
                file_path = parts_text.strip()
                content = ""

            try:
                result = self.file_tool.create_file(file_path, content)
                if result["success"]:
                    return f"File created successfully: {result['message']}"
                else:
                    return f"Error creating file: {result['error']}"
            except Exception as e:
                return f"Error creating file: {e}"

        elif operation == "create_directory":
            # Create a directory
            if len(args_parts) < 2:
                return "Error: No directory path specified."

            directory_path = args_parts[1]

            try:
                result = self.file_tool.create_directory(directory_path)
                if result["success"]:
                    return f"Directory created successfully: {result['message']}"
                else:
                    return f"Error creating directory: {result['error']}"
            except Exception as e:
                return f"Error creating directory: {e}"

        elif operation == "edit_file":
            # Edit a file with various operations
            if len(args_parts) < 2:
                return "Error: No edit arguments specified."

            # Parse edit arguments: file_path operation [line_number] content
            edit_args = args_parts[1].split(maxsplit=3)
            if len(edit_args) < 3:
                return "Error: Insufficient edit arguments. Format: file_path operation [line_number] content"

            file_path = edit_args[0]
            edit_operation = edit_args[1]

            # Check if line number is provided
            try:
                line_number = int(edit_args[2])
                content = edit_args[3] if len(edit_args) > 3 else ""
            except (ValueError, IndexError):
                line_number = None
                content = edit_args[2] if len(edit_args) > 2 else ""

            try:
                result = self.file_tool.edit_file(file_path, content, line_number, edit_operation)
                if result["success"]:
                    return f"File edited successfully: {result['message']}"
                else:
                    return f"Error editing file: {result['error']}"
            except Exception as e:
                return f"Error editing file: {e}"

        elif operation == "insert_edit_into_file":
            # Insert content at specific location
            if len(args_parts) < 2:
                return "Error: No insert arguments specified."

            # Parse: file_path line_number [column] content
            insert_args = args_parts[1].split(maxsplit=3)
            if len(insert_args) < 3:
                return "Error: Insufficient insert arguments. Format: file_path line_number [column] content"

            file_path = insert_args[0]
            try:
                line_number = int(insert_args[1])
            except ValueError:
                return "Error: Invalid line number."

            # Check if column is provided
            try:
                column = int(insert_args[2])
                content = insert_args[3] if len(insert_args) > 3 else ""
            except (ValueError, IndexError):
                column = 0
                content = insert_args[2] if len(insert_args) > 2 else ""

            try:
                result = self.file_tool.insert_edit_into_file(file_path, content, line_number, column)
                if result["success"]:
                    return f"Content inserted successfully: {result['message']}"
                else:
                    return f"Error inserting content: {result['error']}"
            except Exception as e:
                return f"Error inserting content: {e}"

        elif operation == "file_search":
            # Advanced file search
            if len(args_parts) < 2:
                return "Error: No search pattern specified."

            # Parse: pattern [search_type] [recursive]
            search_args = args_parts[1].split(maxsplit=2)
            pattern = search_args[0]
            search_type = search_args[1] if len(search_args) > 1 else "glob"
            recursive = search_args[2].lower() == "true" if len(search_args) > 2 else True

            try:
                result = self.file_tool.file_search(pattern, search_type, recursive)
                if result["success"]:
                    return f"File search results:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in file search: {result['error']}"
            except Exception as e:
                return f"Error in file search: {e}"

        elif operation == "list_code_usages":
            # Find symbol usages
            if len(args_parts) < 2:
                return "Error: No symbol specified."

            symbol = args_parts[1]

            try:
                result = self.file_tool.list_code_usages(symbol)
                if result["success"]:
                    return f"Code usages for '{symbol}':\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error finding code usages: {result['error']}"
            except Exception as e:
                return f"Error finding code usages: {e}"

        elif operation == "get_changed_files":
            # Get Git changed files
            since = args_parts[1] if len(args_parts) > 1 else None

            try:
                result = self.file_tool.get_changed_files(since)
                if result["success"]:
                    return f"Changed files:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error getting changed files: {result['error']}"
            except Exception as e:
                return f"Error getting changed files: {e}"

        elif operation == "get_errors":
            # Get file errors
            file_path = args_parts[1] if len(args_parts) > 1 else None

            try:
                result = self.file_tool.get_errors(file_path)
                if result["success"]:
                    return f"File errors:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error getting file errors: {result['error']}"
            except Exception as e:
                return f"Error getting file errors: {e}"

        elif operation == "semantic_search":
            # Semantic search across codebase
            if len(args_parts) < 2:
                return "Error: No search query specified."

            query = args_parts[1]

            try:
                result = self.file_tool.semantic_search(query)
                if result["success"]:
                    return f"Semantic search results for '{query}':\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in semantic search: {result['error']}"
            except Exception as e:
                return f"Error in semantic search: {e}"

        elif operation == "get_project_setup_info":
            # Get project setup information
            try:
                result = self.file_tool.get_project_setup_info()
                if result["success"]:
                    return f"Project setup information:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error getting project info: {result['error']}"
            except Exception as e:
                return f"Error getting project info: {e}"

        else:
            return f"Error: Unknown file operation: {operation}. Available operations: read, write, append, delete, list, search, grep, create_file, create_directory, edit_file, insert_edit_into_file, file_search, list_code_usages, get_changed_files, get_errors, semantic_search, get_project_setup_info"

    def _execute_code(self, args: str) -> str:
        """Execute code in various programming languages.

        Args:
            args: The arguments for the code execution.

        Returns:
            The result of the execution.
        """
        # Parse the arguments
        args_parts = args.strip().split("\n", 1)
        if len(args_parts) < 2:
            return "Error: No code or language specified."

        language = args_parts[0].strip().lower()
        code = args_parts[1]

        # Check if the language is supported
        supported_languages = self.code_tool.get_supported_languages()
        if language not in supported_languages:
            return f"Error: Unsupported language '{language}'. Supported languages: {', '.join(supported_languages)}"

        # Log the execution attempt
        logger.info(f"Executing {language} code...")

        try:
            # Execute the code with proper error handling
            stdout, stderr, return_code = self.code_tool.execute(code, language)

            # Format the response based on the execution result
            if return_code != 0:
                # Execution failed
                error_message = stderr.strip() if stderr.strip() else "Unknown error"
                logger.error(f"{language.capitalize()} execution failed with return code {return_code}: {error_message}")
                return f"{language.capitalize()} execution error (return code {return_code}):\n\n{stderr}"
            else:
                # Execution succeeded
                if stdout.strip():
                    logger.info(f"{language.capitalize()} execution succeeded with output")
                    return f"{language.capitalize()} execution output:\n\n{stdout}"
                else:
                    logger.info(f"{language.capitalize()} execution succeeded with no output")
                    return f"{language.capitalize()} code executed successfully with no output."

        except Exception as e:
            # Handle any exceptions during execution
            error_details = str(e)
            logger.error(f"Error executing {language} code: {error_details}")

            # Provide a more detailed error message
            if "timeout" in error_details.lower():
                return f"Error: {language.capitalize()} code execution timed out after 30 seconds. Please optimize your code or break it into smaller parts."
            elif "not found" in error_details.lower():
                return f"Error: Required interpreter or compiler for {language} not found. Please ensure {language} is installed on the system."
            else:
                return f"Error executing {language} code: {error_details}"

    def _execute_web(self, args: str) -> str:
        """Execute a web operation.

        Args:
            args: The arguments for the web operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No web operation specified."

        operation = args_parts[0]

        if operation == "search":
            # Search the web
            if len(args_parts) < 2:
                return "Error: No search query specified."

            query = args_parts[1]

            try:
                results = self.web_tool.search(query)
                return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching the web: {e}"

        elif operation == "fetch":
            # Fetch a URL
            if len(args_parts) < 2:
                return "Error: No URL specified."

            url = args_parts[1]

            try:
                content, metadata = self.web_tool.fetch_url(url)
                return f"Content from {url}:\n\nTitle: {metadata.get('title', 'N/A')}\n\n{content[:2000]}..."
            except Exception as e:
                return f"Error fetching URL: {e}"

        elif operation == "fetch_webpage":
            # Fetch and parse webpage content with advanced extraction
            if len(args_parts) < 2:
                return "Error: No URL specified for webpage fetch."

            # Parse: url [extract_text] [extract_links] [extract_images]
            fetch_args = args_parts[1].split()
            url = fetch_args[0]
            extract_text = fetch_args[1].lower() != "false" if len(fetch_args) > 1 else True
            extract_links = fetch_args[2].lower() == "true" if len(fetch_args) > 2 else False
            extract_images = fetch_args[3].lower() == "true" if len(fetch_args) > 3 else False

            try:
                result = self.web_tool.fetch_webpage(url, extract_text, extract_links, extract_images)
                if result["success"]:
                    return f"Webpage fetch result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error fetching webpage: {result['error']}"
            except Exception as e:
                return f"Error fetching webpage: {e}"

        elif operation == "open_simple_browser":
            # Open URL in a simple browser simulation
            if len(args_parts) < 2:
                return "Error: No URL specified for browser."

            # Parse: url [wait_time] [take_screenshot]
            browser_args = args_parts[1].split()
            url = browser_args[0]
            wait_time = int(browser_args[1]) if len(browser_args) > 1 else 3
            take_screenshot = browser_args[2].lower() == "true" if len(browser_args) > 2 else False

            try:
                result = self.web_tool.open_simple_browser(url, wait_time, take_screenshot)
                if result["success"]:
                    return f"Browser session result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error opening browser: {result['error']}"
            except Exception as e:
                return f"Error opening browser: {e}"

        elif operation == "github_repo":
            # Access GitHub repositories
            if len(args_parts) < 2:
                return "Error: No GitHub repository URL specified."

            # Parse: repo_url [action] [file_path]
            github_args = args_parts[1].split(maxsplit=2)
            repo_url = github_args[0]
            action = github_args[1] if len(github_args) > 1 else "info"
            file_path = github_args[2] if len(github_args) > 2 else None

            try:
                result = self.web_tool.github_repo(repo_url, action, file_path)
                if result["success"]:
                    return f"GitHub repository result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error accessing GitHub repo: {result['error']}"
            except Exception as e:
                return f"Error accessing GitHub repo: {e}"

        elif operation == "semantic_web_search":
            # Intelligent web search with semantic understanding
            if len(args_parts) < 2:
                return "Error: No search query specified for semantic search."

            # Parse: query [search_type] [num_results] [filter_domains]
            search_args = args_parts[1].split(maxsplit=3)
            query = search_args[0]
            search_type = search_args[1] if len(search_args) > 1 else "general"
            num_results = int(search_args[2]) if len(search_args) > 2 else 10
            filter_domains = search_args[3].split(',') if len(search_args) > 3 else None

            try:
                result = self.web_tool.semantic_web_search(query, search_type, num_results, filter_domains)
                if result["success"]:
                    return f"Semantic search results:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in semantic search: {result['error']}"
            except Exception as e:
                return f"Error in semantic search: {e}"

        elif operation == "get_search_view_results":
            # Get comprehensive search results with content extraction
            if len(args_parts) < 2:
                return "Error: No search query specified for comprehensive search."

            # Parse: query [max_pages] [extract_content]
            search_args = args_parts[1].split()
            query = search_args[0]
            max_pages = int(search_args[1]) if len(search_args) > 1 else 3
            extract_content = search_args[2].lower() != "false" if len(search_args) > 2 else True

            try:
                result = self.web_tool.get_search_view_results(query, max_pages, extract_content)
                if result["success"]:
                    return f"Comprehensive search results:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error getting comprehensive search results: {result['error']}"
            except Exception as e:
                return f"Error getting comprehensive search results: {e}"

        elif operation == "retrieval_augmented_generation":
            # Implement RAG for web content
            if len(args_parts) < 2:
                return "Error: No query specified for RAG."

            # Parse: query [context_sources] [max_context_length]
            rag_args = args_parts[1].split(maxsplit=2)
            query = rag_args[0]
            context_sources = rag_args[1].split(',') if len(rag_args) > 1 else None
            max_context_length = int(rag_args[2]) if len(rag_args) > 2 else 2000

            try:
                result = self.web_tool.retrieval_augmented_generation(query, context_sources, max_context_length)
                if result["success"]:
                    return f"RAG result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in RAG: {result['error']}"
            except Exception as e:
                return f"Error in RAG: {e}"

        else:
            return f"Error: Unknown web operation: {operation}. Available operations: search, fetch, fetch_webpage, open_simple_browser, github_repo, semantic_web_search, get_search_view_results, retrieval_augmented_generation"

    def _execute_codebase(self, args: str) -> str:
        """Execute a codebase operation.

        Args:
            args: The arguments for the codebase operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No codebase operation specified."

        operation = args_parts[0]

        if operation == "find":
            # Find files
            pattern = args_parts[1] if len(args_parts) > 1 else "*"

            try:
                files = self.codebase_tool.find_files(pattern)
                return f"Files matching {pattern}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding files: {e}"

        elif operation == "find_code":
            # Find code files
            try:
                files = self.codebase_tool.find_code_files()
                return f"Code files found:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding code files: {e}"

        elif operation == "search":
            # Search for a pattern in code files
            if len(args_parts) < 2:
                return "Error: No search pattern specified."

            # Parse the pattern and file pattern
            search_parts = args_parts[1].split(maxsplit=1)
            if len(search_parts) < 2:
                pattern = search_parts[0]
                file_pattern = "*"
            else:
                pattern = search_parts[0]
                file_pattern = search_parts[1]

            try:
                results = self.codebase_tool.search_code(pattern, file_pattern)
                return f"Search results for {pattern} in {file_pattern}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching code: {e}"

        elif operation == "analyze":
            # Analyze a code file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                results = self.codebase_tool.analyze_file(file_path)
                return f"Analysis results for {file_path}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error analyzing file: {e}"

        else:
            return f"Error: Unknown codebase operation: {operation}"

    def _execute_vision(self, args: str) -> str:
        """Execute a vision operation.

        Args:
            args: The arguments for the vision operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No vision operation specified."

        operation = args_parts[0]

        if operation == "take_screenshot":
            # Take a screenshot
            path = args_parts[1] if len(args_parts) > 1 else None

            try:
                screenshot_path = self.vision_tool.take_screenshot(path)
                return f"Screenshot taken and saved to: {screenshot_path}"
            except Exception as e:
                return f"Error taking screenshot: {e}"

        elif operation == "load_image":
            # Load an image
            if len(args_parts) < 2:
                return "Error: No image path specified."

            image_path = args_parts[1]

            try:
                image = self.vision_tool.load_image(image_path)
                return f"Image loaded: {image_path} (Size: {image.width}x{image.height})"
            except Exception as e:
                return f"Error loading image: {e}"

        else:
            return f"Error: Unknown vision operation: {operation}"

    def _execute_tmux(self, args: str) -> str:
        """Execute a tmux operation.

        Args:
            args: The arguments for the tmux operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No tmux operation specified."

        operation = args_parts[0]

        if operation == "new_session":
            # Create a new tmux session
            if len(args_parts) < 2:
                return "Error: No command specified."

            command = args_parts[1]

            try:
                session_id, output = self.tmux_tool.new_session(command)
                if session_id:
                    return f"Tmux session created: {session_id}"
                else:
                    return f"Error creating tmux session: {output}"
            except Exception as e:
                return f"Error creating tmux session: {e}"

        elif operation == "send_keys":
            # Send keys to a tmux session
            if len(args_parts) < 2:
                return "Error: No session ID or keys specified."

            # Parse the session ID and keys
            send_parts = args_parts[1].split(maxsplit=1)
            if len(send_parts) < 2:
                return "Error: No keys specified."

            session_id = send_parts[0]
            keys = send_parts[1]

            try:
                success, message = self.tmux_tool.send_keys(session_id, keys)
                if success:
                    return f"Keys sent to tmux session: {session_id}"
                else:
                    return f"Error sending keys to tmux session: {message}"
            except Exception as e:
                return f"Error sending keys to tmux session: {e}"

        elif operation == "inspect_pane":
            # Inspect a tmux pane
            if len(args_parts) < 2:
                return "Error: No session ID specified."

            session_id = args_parts[1]

            try:
                success, content = self.tmux_tool.inspect_pane(session_id)
                if success:
                    return f"Tmux pane content for session {session_id}:\n\n{content}"
                else:
                    return f"Error inspecting tmux pane: {content}"
            except Exception as e:
                return f"Error inspecting tmux pane: {e}"

        elif operation == "kill_session":
            # Kill a tmux session
            if len(args_parts) < 2:
                return "Error: No session ID specified."

            session_id = args_parts[1]

            try:
                success, message = self.tmux_tool.kill_session(session_id)
                if success:
                    return f"Tmux session killed: {session_id}"
                else:
                    return f"Error killing tmux session: {message}"
            except Exception as e:
                return f"Error killing tmux session: {e}"

        elif operation == "list_sessions":
            # List tmux sessions
            try:
                sessions = self.tmux_tool.list_sessions()
                if sessions:
                    return f"Tmux sessions:\n\n{json.dumps(sessions, indent=2)}"
                else:
                    return "No tmux sessions found."
            except Exception as e:
                return f"Error listing tmux sessions: {e}"

        else:
            return f"Error: Unknown tmux operation: {operation}"

    def _execute_patch(self, args: str) -> str:
        """Execute a patch operation.

        Args:
            args: The arguments for the patch operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No patch operation specified."

        operation = args_parts[0]

        if operation == "apply_patch":
            # Apply a patch to a file
            if len(args_parts) < 2:
                return "Error: No file path or patch content specified."

            # Parse the file path, original content, and updated content
            patch_parts = args_parts[1].split(maxsplit=2)
            if len(patch_parts) < 3:
                return "Error: Missing file path, original content, or updated content."

            file_path = patch_parts[0]
            original = patch_parts[1]
            updated = patch_parts[2]

            try:
                success, message = self.patch_tool.apply_patch(file_path, original, updated)
                if success:
                    return f"Patch applied to file: {file_path}"
                else:
                    return f"Error applying patch: {message}"
            except Exception as e:
                return f"Error applying patch: {e}"

        elif operation == "apply_git_style_patch":
            # Apply a git-style patch to a file
            if len(args_parts) < 2:
                return "Error: No file path or patch content specified."

            # Parse the file path and patch content
            patch_parts = args_parts[1].split(maxsplit=1)
            if len(patch_parts) < 2:
                return "Error: Missing file path or patch content."

            file_path = patch_parts[0]
            patch_content = patch_parts[1]

            try:
                success, message = self.patch_tool.apply_git_style_patch(file_path, patch_content)
                if success:
                    return f"Git-style patch applied to file: {file_path}"
                else:
                    return f"Error applying git-style patch: {message}"
            except Exception as e:
                return f"Error applying git-style patch: {e}"

        elif operation == "generate_diff":
            # Generate a diff between two strings
            if len(args_parts) < 2:
                return "Error: No original or updated content specified."

            # Parse the original and updated content
            diff_parts = args_parts[1].split(maxsplit=1)
            if len(diff_parts) < 2:
                return "Error: Missing original or updated content."

            original = diff_parts[0]
            updated = diff_parts[1]

            try:
                diff = self.patch_tool.generate_diff(original, updated)
                return f"Diff:\n\n{diff}"
            except Exception as e:
                return f"Error generating diff: {e}"

        elif operation == "generate_git_style_patch":
            # Generate a git-style patch between two strings
            if len(args_parts) < 2:
                return "Error: No original or updated content specified."

            # Parse the original and updated content
            patch_parts = args_parts[1].split(maxsplit=1)
            if len(patch_parts) < 2:
                return "Error: Missing original or updated content."

            original = patch_parts[0]
            updated = patch_parts[1]

            try:
                patch = self.patch_tool.generate_git_style_patch(original, updated)
                return f"Git-style patch:\n\n{patch}"
            except Exception as e:
                return f"Error generating git-style patch: {e}"

        else:
            return f"Error: Unknown patch operation: {operation}"

    def _execute_browser(self, args: str) -> str:
        """Execute a browser operation.

        Args:
            args: The arguments for the browser operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No browser operation specified."

        operation = args_parts[0]

        if operation == "read_url":
            # Read a URL
            if len(args_parts) < 2:
                return "Error: No URL specified."

            url = args_parts[1]

            try:
                success, content = self.browser_tool.read_url(url)
                if success:
                    return f"Content from {url}:\n\n{content[:2000]}..."
                else:
                    return f"Error reading URL: {content}"
            except Exception as e:
                return f"Error reading URL: {e}"

        elif operation == "screenshot_url":
            # Take a screenshot of a URL
            if len(args_parts) < 2:
                return "Error: No URL specified."

            # Parse the URL and path
            screenshot_parts = args_parts[1].split(maxsplit=1)
            url = screenshot_parts[0]
            path = screenshot_parts[1] if len(screenshot_parts) > 1 else None

            try:
                success, screenshot_path = self.browser_tool.screenshot_url(url, path)
                if success:
                    return f"Screenshot of {url} saved to: {screenshot_path}"
                else:
                    return f"Error taking screenshot: {screenshot_path}"
            except Exception as e:
                return f"Error taking screenshot: {e}"

        elif operation == "search":
            # Search the web
            if len(args_parts) < 2:
                return "Error: No search query specified."

            # Parse the query and engine
            search_parts = args_parts[1].split(maxsplit=1)
            query = search_parts[0]
            engine = search_parts[1] if len(search_parts) > 1 else "google"

            try:
                success, results = self.browser_tool.search(query, engine)
                if success:
                    return f"Search results for '{query}' using {engine}:\n\n{json.dumps(results, indent=2)}"
                else:
                    return f"Error searching: {results}"
            except Exception as e:
                return f"Error searching: {e}"

        else:
            return f"Error: Unknown browser operation: {operation}"

    def _execute_search(self, args: str) -> str:
        """Execute a search operation using the WebInfoManager.

        Args:
            args: The arguments for the search operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No search query specified."

        query = args_parts[0]
        num_results = 5  # Default number of results

        # Check if number of results is specified
        if len(args_parts) > 1:
            try:
                num_results = int(args_parts[1])
            except ValueError:
                # If the second argument is not a number, assume it's part of the query
                query = args

        try:
            # Use the WebInfoManager for more reliable results with fallbacks
            results = self.web_info_manager.search(query, num_results=num_results)

            if results and len(results) > 0:
                return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"

            # If all methods fail, return an error
            return f"No search results found for '{query}'. Please try a different query or search method."

        except Exception as e:
            return f"Error searching: {e}"

    def _execute_scrape(self, args: str) -> str:
        """Execute a web scraping operation.

        Args:
            args: The arguments for the scraping operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No URL specified."

        url = args_parts[0]
        use_fallbacks = True
        if len(args_parts) > 1 and args_parts[1].lower() == "no_fallbacks":
            use_fallbacks = False

        try:
            # Use the WebInfoManager for more reliable content extraction with fallbacks
            print(f"Scraping '{url}' using WebInfoManager...")
            success, content, metadata = self.web_info_manager.fetch_url(url, use_fallbacks=use_fallbacks)

            if success:
                title = metadata.get("title", "")
                title_info = f"Title: {title}\n\n" if title else ""
                method_info = f"Method: {metadata.get('method', 'unknown')}\n\n"

                # Truncate content if too long
                content_preview = content[:3000] + "..." if len(content) > 3000 else content

                return f"Content from {url}:\n\n{title_info}{method_info}{content_preview}"
            else:
                return f"Error scraping URL: {content}"

        except Exception as e:
            return f"Error scraping URL: {e}"

    def _execute_info(self, args: str) -> str:
        """Execute an information retrieval and synthesis operation.

        Args:
            args: The arguments for the information operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=2)
        if not args_parts:
            return "Error: No operation specified."

        operation = args_parts[0]

        if operation == "retrieve":
            # Retrieve information about a topic
            if len(args_parts) < 2:
                return "Error: No query specified."

            query = args_parts[1]
            max_sources = int(args_parts[2]) if len(args_parts) > 2 and args_parts[2].isdigit() else 3

            try:
                # Use the WebInfoManager to retrieve and synthesize information
                print(f"Retrieving information about '{query}' using WebInfoManager...")
                information = self.web_info_manager.retrieve_information(query, max_sources=max_sources)

                return f"Information about '{query}':\n\n{information}"
            except Exception as e:
                return f"Error retrieving information: {e}"

        elif operation == "extract":
            # Extract key information from text
            if len(args_parts) < 2:
                return "Error: No text specified."

            text = args_parts[1]
            max_length = int(args_parts[2]) if len(args_parts) > 2 and args_parts[2].isdigit() else 1000

            try:
                # Use the InformationSynthesizer to extract key information
                print(f"Extracting key information from text...")
                extracted = self.info_synthesizer.extract_key_information(text, max_length=max_length)

                return f"Extracted information:\n\n{extracted}"
            except Exception as e:
                return f"Error extracting information: {e}"

        elif operation == "format":
            # Format information
            if len(args_parts) < 2:
                return "Error: No text specified."

            text = args_parts[1]
            format_type = args_parts[2] if len(args_parts) > 2 else "markdown"

            try:
                # Use the InformationSynthesizer to format information
                print(f"Formatting information as {format_type}...")
                formatted = self.info_synthesizer.format_information(text, format_type=format_type)

                return f"Formatted information ({format_type}):\n\n{formatted}"
            except Exception as e:
                return f"Error formatting information: {e}"

        else:
            return f"Error: Unknown information operation: {operation}"

    def _execute_rag(self, args: str) -> str:
        """Execute a RAG operation.

        Args:
            args: The arguments for the RAG operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No RAG operation specified."

        operation = args_parts[0]

        if operation == "create_index":
            # Create an index from a list of documents
            if len(args_parts) < 2:
                return "Error: No documents specified."

            try:
                # Parse the documents as JSON
                documents = json.loads(args_parts[1])

                success, message = self.rag_tool.create_index(documents)
                if success:
                    return message
                else:
                    return f"Error creating index: {message}"
            except json.JSONDecodeError:
                # Treat the argument as a single document
                documents = [args_parts[1]]

                success, message = self.rag_tool.create_index(documents)
                if success:
                    return message
                else:
                    return f"Error creating index: {message}"
            except Exception as e:
                return f"Error creating index: {e}"

        elif operation == "add_documents":
            # Add documents to the index
            if len(args_parts) < 2:
                return "Error: No documents specified."

            try:
                # Parse the documents as JSON
                documents = json.loads(args_parts[1])

                success, message = self.rag_tool.add_documents(documents)
                if success:
                    return message
                else:
                    return f"Error adding documents: {message}"
            except json.JSONDecodeError:
                # Treat the argument as a single document
                documents = [args_parts[1]]

                success, message = self.rag_tool.add_documents(documents)
                if success:
                    return message
                else:
                    return f"Error adding documents: {message}"
            except Exception as e:
                return f"Error adding documents: {e}"

        elif operation == "search":
            # Search the index
            if len(args_parts) < 2:
                return "Error: No query specified."

            # Parse the query and k
            search_parts = args_parts[1].split(maxsplit=1)
            query = search_parts[0]
            k = int(search_parts[1]) if len(search_parts) > 1 else 5

            try:
                success, results = self.rag_tool.search(query, k)
                if success:
                    return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"
                else:
                    return f"Error searching: {results}"
            except Exception as e:
                return f"Error searching: {e}"

        elif operation == "save_index":
            # Save the index to a file
            if len(args_parts) < 2:
                return "Error: No path specified."

            path = args_parts[1]

            try:
                success, message = self.rag_tool.save_index(path)
                if success:
                    return message
                else:
                    return f"Error saving index: {message}"
            except Exception as e:
                return f"Error saving index: {e}"

        elif operation == "load_index":
            # Load the index from a file
            if len(args_parts) < 2:
                return "Error: No path specified."

            path = args_parts[1]

            try:
                success, message = self.rag_tool.load_index(path)
                if success:
                    return message
                else:
                    return f"Error loading index: {message}"
            except Exception as e:
                return f"Error loading index: {e}"

        else:
            return f"Error: Unknown RAG operation: {operation}"

    def _execute_ai_assistant(self, args: str) -> str:
        """Execute an AI Code Assistant operation.

        Args:
            args: The arguments for the AI assistant operation.

        Returns:
            The result of the operation.
        """
        import uuid

        # Parse the arguments
        args_parts = args.strip().split(maxsplit=2)
        if not args_parts:
            return "Error: No AI assistant operation specified."

        operation = args_parts[0]

        if operation == "analyze":
            # Analyze code
            if len(args_parts) < 2:
                return "Error: No code specified for analysis."

            code = args_parts[1]
            language = args_parts[2] if len(args_parts) > 2 else None

            try:
                request = AssistantRequest(
                    request_id=str(uuid.uuid4()),
                    request_type="analyze",
                    code=code,
                    language=language,
                    prompt=None,
                    context={},
                    preferences={},
                    constraints=[]
                )

                response = self.ai_code_assistant.process_request(request)

                if response.success:
                    result = response.result
                    return f"Code Analysis Results:\n\n" \
                           f"Language: {result.get('language', 'unknown')}\n" \
                           f"Confidence: {response.confidence_score:.2f}\n" \
                           f"Processing Time: {response.processing_time:.2f}s\n\n" \
                           f"Analysis Summary:\n{json.dumps(result.get('self_analysis_summary', {}), indent=2)}\n\n" \
                           f"Recommendations:\n" + "\n".join(f"- {rec}" for rec in response.recommendations)
                else:
                    return f"Error analyzing code: {response.result.get('error', 'Unknown error')}"

            except Exception as e:
                return f"Error analyzing code: {e}"

        elif operation == "generate":
            # Generate code
            if len(args_parts) < 2:
                return "Error: No prompt specified for code generation."

            prompt = args_parts[1]
            language = args_parts[2] if len(args_parts) > 2 else "python"

            try:
                request = AssistantRequest(
                    request_id=str(uuid.uuid4()),
                    request_type="generate",
                    code=None,
                    language=language,
                    prompt=prompt,
                    context={"language": language},
                    preferences={},
                    constraints=[]
                )

                response = self.ai_code_assistant.process_request(request)

                if response.success:
                    result = response.result
                    generated_code = result.get("generated_code", "")
                    return f"Generated {language} Code:\n\n```{language}\n{generated_code}\n```\n\n" \
                           f"Confidence: {response.confidence_score:.2f}\n" \
                           f"Processing Time: {response.processing_time:.2f}s\n\n" \
                           f"Recommendations:\n" + "\n".join(f"- {rec}" for rec in response.recommendations)
                else:
                    return f"Error generating code: {response.result.get('error', 'Unknown error')}"

            except Exception as e:
                return f"Error generating code: {e}"

        elif operation == "optimize":
            # Optimize code
            if len(args_parts) < 2:
                return "Error: No code specified for optimization."

            code = args_parts[1]
            language = args_parts[2] if len(args_parts) > 2 else None

            try:
                request = AssistantRequest(
                    request_id=str(uuid.uuid4()),
                    request_type="optimize",
                    code=code,
                    language=language,
                    prompt=None,
                    context={"performance": True},
                    preferences={},
                    constraints=[]
                )

                response = self.ai_code_assistant.process_request(request)

                if response.success:
                    result = response.result
                    optimized_code = result.get("optimized_code", code)
                    improvements = result.get("performance_improvement", {})

                    return f"Code Optimization Results:\n\n" \
                           f"Original Code:\n```{language or 'text'}\n{result.get('original_code', '')[:500]}...\n```\n\n" \
                           f"Optimized Code:\n```{language or 'text'}\n{optimized_code[:500]}...\n```\n\n" \
                           f"Improvements: {json.dumps(improvements, indent=2)}\n" \
                           f"Applied Optimizations: {', '.join(response.optimizations_applied)}\n" \
                           f"Confidence: {response.confidence_score:.2f}\n\n" \
                           f"Recommendations:\n" + "\n".join(f"- {rec}" for rec in response.recommendations)
                else:
                    return f"Error optimizing code: {response.result.get('error', 'Unknown error')}"

            except Exception as e:
                return f"Error optimizing code: {e}"

        elif operation == "debug":
            # Debug code
            if len(args_parts) < 2:
                return "Error: No code specified for debugging."

            code = args_parts[1]
            language = args_parts[2] if len(args_parts) > 2 else None

            try:
                request = AssistantRequest(
                    request_id=str(uuid.uuid4()),
                    request_type="debug",
                    code=code,
                    language=language,
                    prompt=None,
                    context={},
                    preferences={},
                    constraints=[]
                )

                response = self.ai_code_assistant.process_request(request)

                if response.success:
                    result = response.result
                    debug_suggestions = result.get("debug_suggestions", [])

                    return f"Code Debugging Results:\n\n" \
                           f"Execution Analysis: {json.dumps(result.get('execution_analysis', {}), indent=2)}\n\n" \
                           f"Debug Suggestions:\n" + "\n".join(f"- {sugg}" for sugg in debug_suggestions) + "\n\n" \
                           f"Confidence: {response.confidence_score:.2f}\n\n" \
                           f"Recommendations:\n" + "\n".join(f"- {rec}" for rec in response.recommendations)
                else:
                    return f"Error debugging code: {response.result.get('error', 'Unknown error')}"

            except Exception as e:
                return f"Error debugging code: {e}"

        elif operation == "refactor":
            # Refactor code
            if len(args_parts) < 2:
                return "Error: No code specified for refactoring."

            code = args_parts[1]
            language = args_parts[2] if len(args_parts) > 2 else None

            try:
                request = AssistantRequest(
                    request_id=str(uuid.uuid4()),
                    request_type="refactor",
                    code=code,
                    language=language,
                    prompt=None,
                    context={},
                    preferences={},
                    constraints=[]
                )

                response = self.ai_code_assistant.process_request(request)

                if response.success:
                    result = response.result
                    refactored_code = result.get("refactored_code", code)

                    return f"Code Refactoring Results:\n\n" \
                           f"Original Code:\n```{language or 'text'}\n{result.get('original_code', '')[:500]}...\n```\n\n" \
                           f"Refactored Code:\n```{language or 'text'}\n{refactored_code[:500]}...\n```\n\n" \
                           f"Applied Refactorings: {json.dumps(result.get('applied_refactorings', []), indent=2)}\n" \
                           f"Confidence: {response.confidence_score:.2f}\n\n" \
                           f"Recommendations:\n" + "\n".join(f"- {rec}" for rec in response.recommendations)
                else:
                    return f"Error refactoring code: {response.result.get('error', 'Unknown error')}"

            except Exception as e:
                return f"Error refactoring code: {e}"

        elif operation == "stats":
            # Get AI assistant statistics
            try:
                stats = self.ai_code_assistant.get_assistant_statistics()
                return f"AI Code Assistant Statistics:\n\n{json.dumps(stats, indent=2)}"
            except Exception as e:
                return f"Error getting statistics: {e}"

        else:
            return f"Error: Unknown AI assistant operation: {operation}\n\n" \
                   f"Available operations: analyze, generate, optimize, debug, refactor, stats"

    def _execute_test(self, args: str) -> str:
        """Execute a testing/debugging operation.

        Args:
            args: The arguments for the testing operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No testing operation specified."

        operation = args_parts[0]

        if operation == "test_search":
            # Find tests related to source files
            source_file = args_parts[1] if len(args_parts) > 1 else None

            try:
                result = self.testing_tool.test_search(source_file)
                if result["success"]:
                    return f"Test search results:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in test search: {result['error']}"
            except Exception as e:
                return f"Error in test search: {e}"

        elif operation == "test_failure":
            # Capture and analyze test failure messages
            test_file = args_parts[1] if len(args_parts) > 1 else None

            try:
                result = self.testing_tool.test_failure(test_file)
                if result["success"]:
                    return f"Test failure analysis:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error analyzing test failures: {result['error']}"
            except Exception as e:
                return f"Error analyzing test failures: {e}"

        elif operation == "run_tests":
            # Automatically execute test suites
            test_pattern = args_parts[1] if len(args_parts) > 1 else None

            try:
                result = self.testing_tool.run_tests(test_pattern)
                if result["success"]:
                    return f"Test execution results:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error running tests: {result['error']}"
            except Exception as e:
                return f"Error running tests: {e}"

        elif operation == "autonomous_debugger":
            # Trace, identify and fix bugs automatically
            if len(args_parts) < 2:
                return "Error: No file path specified for debugging."

            file_path = args_parts[1]

            try:
                result = self.testing_tool.autonomous_debugger(file_path)
                if result["success"]:
                    return f"Autonomous debugging results:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in autonomous debugging: {result['error']}"
            except Exception as e:
                return f"Error in autonomous debugging: {e}"

        elif operation == "lint_check":
            # Run lint/static analysis on code
            file_path = args_parts[1] if len(args_parts) > 1 else None

            try:
                result = self.testing_tool.lint_check(file_path)
                if result["success"]:
                    return f"Lint check results:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in lint check: {result['error']}"
            except Exception as e:
                return f"Error in lint check: {e}"

        elif operation == "self_repair":
            # AI-suggested code fixes based on errors
            if len(args_parts) < 2:
                return "Error: No file path specified for self repair."

            file_path = args_parts[1]

            try:
                result = self.testing_tool.self_repair(file_path)
                if result["success"]:
                    return f"Self repair results:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in self repair: {result['error']}"
            except Exception as e:
                return f"Error in self repair: {e}"

        elif operation == "code_linting_static_analysis":
            # Combine all error & code checkers
            target = args_parts[1] if len(args_parts) > 1 else None

            try:
                result = self.testing_tool.code_linting_static_analysis(target)
                if result["success"]:
                    return f"Comprehensive code analysis:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in comprehensive analysis: {result['error']}"
            except Exception as e:
                return f"Error in comprehensive analysis: {e}"

        else:
            return f"Error: Unknown testing operation: {operation}. Available operations: test_search, test_failure, run_tests, autonomous_debugger, lint_check, self_repair, code_linting_static_analysis"

    def _execute_workflow(self, args: str) -> str:
        """Execute a workflow/smart agent operation.

        Args:
            args: The arguments for the workflow operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No workflow operation specified."

        operation = args_parts[0]

        if operation == "create_new_workspace":
            # Create and setup new project workspace
            if len(args_parts) < 2:
                return "Error: No workspace name specified."

            # Parse: workspace_name [template] [initialize_git]
            workspace_args = args_parts[1].split()
            workspace_name = workspace_args[0]
            template = workspace_args[1] if len(workspace_args) > 1 else None
            initialize_git = workspace_args[2].lower() != "false" if len(workspace_args) > 2 else True

            try:
                result = self.workflow_tool.create_new_workspace(workspace_name, template, initialize_git)
                if result["success"]:
                    return f"Workspace creation result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error creating workspace: {result['error']}"
            except Exception as e:
                return f"Error creating workspace: {e}"

        elif operation == "run_vscode_command":
            # Execute VSCode commands and extensions
            if len(args_parts) < 2:
                return "Error: No VSCode command specified."

            # Parse: command [args...]
            vscode_args = args_parts[1].split()
            command = vscode_args[0]
            cmd_args = vscode_args[1:] if len(vscode_args) > 1 else None

            try:
                result = self.workflow_tool.run_vscode_command(command, cmd_args)
                if result["success"]:
                    return f"VSCode command result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error executing VSCode command: {result['error']}"
            except Exception as e:
                return f"Error executing VSCode command: {e}"

        elif operation == "plan_next_step":
            # AI-powered planning for next development steps
            if len(args_parts) < 2:
                return "Error: No planning context specified."

            # Parse: "current_context" "goal" [previous_steps]
            plan_args = args_parts[1].split(maxsplit=2)
            if len(plan_args) < 2:
                return "Error: Both current_context and goal required."

            current_context = plan_args[0].strip('"')
            goal = plan_args[1].strip('"')
            previous_steps = plan_args[2].split(',') if len(plan_args) > 2 else None

            try:
                result = self.workflow_tool.plan_next_step(current_context, goal, previous_steps)
                if result["success"]:
                    return f"Planning result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in planning: {result['error']}"
            except Exception as e:
                return f"Error in planning: {e}"

        elif operation == "multi_step_loop":
            # Execute multi-step workflows with progress tracking
            if len(args_parts) < 2:
                return "Error: No plan ID specified."

            # Parse: plan_id [execute_steps]
            loop_args = args_parts[1].split()
            plan_id = loop_args[0]
            execute_steps = loop_args[1].lower() == "true" if len(loop_args) > 1 else False

            try:
                result = self.workflow_tool.multi_step_loop(plan_id, execute_steps)
                if result["success"]:
                    return f"Multi-step execution result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in multi-step execution: {result['error']}"
            except Exception as e:
                return f"Error in multi-step execution: {e}"

        elif operation == "context_aware_refactor":
            # Smart code refactoring with context awareness
            if len(args_parts) < 2:
                return "Error: No file path specified for refactoring."

            # Parse: file_path [refactor_type] [target_language]
            refactor_args = args_parts[1].split()
            file_path = refactor_args[0]
            refactor_type = refactor_args[1] if len(refactor_args) > 1 else "general"
            target_language = refactor_args[2] if len(refactor_args) > 2 else None

            try:
                result = self.workflow_tool.context_aware_refactor(file_path, refactor_type, target_language)
                if result["success"]:
                    return f"Refactoring result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in refactoring: {result['error']}"
            except Exception as e:
                return f"Error in refactoring: {e}"

        elif operation == "context_compression":
            # Compress context while preserving important information
            if len(args_parts) < 2:
                return "Error: No context data specified for compression."

            # Parse: "context_data" [max_length] [compression_type]
            compress_args = args_parts[1].split(maxsplit=2)
            context_data = compress_args[0].strip('"')
            max_length = int(compress_args[1]) if len(compress_args) > 1 else 1000
            compression_type = compress_args[2] if len(compress_args) > 2 else "summary"

            try:
                result = self.workflow_tool.context_compression(context_data, max_length, compression_type)
                if result["success"]:
                    return f"Context compression result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in context compression: {result['error']}"
            except Exception as e:
                return f"Error in context compression: {e}"

        elif operation == "context_tracking_memory":
            # Track and manage context memory across sessions
            if len(args_parts) < 2:
                return "Error: No context ID specified for memory operation."

            # Parse: context_id action [context_data]
            memory_args = args_parts[1].split(maxsplit=2)
            context_id = memory_args[0]
            action = memory_args[1] if len(memory_args) > 1 else "retrieve"
            context_data = json.loads(memory_args[2]) if len(memory_args) > 2 and action in ["store", "update"] else {}

            try:
                result = self.workflow_tool.context_tracking_memory(context_id, context_data, action)
                if result["success"]:
                    return f"Context memory result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in context memory operation: {result['error']}"
            except Exception as e:
                return f"Error in context memory operation: {e}"

        else:
            return f"Error: Unknown workflow operation: {operation}. Available operations: create_new_workspace, run_vscode_command, plan_next_step, multi_step_loop, context_aware_refactor, context_compression, context_tracking_memory"

    def _execute_ai_reasoning(self, args: str) -> str:
        """Execute an AI reasoning/NLP operation.

        Args:
            args: The arguments for the AI reasoning operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No AI reasoning operation specified."

        operation = args_parts[0]

        if operation == "natural_language_to_code":
            # Convert natural language to code
            if len(args_parts) < 2:
                return "Error: No description specified for code generation."

            # Parse: "description" [language] [context]
            nl_args = args_parts[1].split(maxsplit=2)
            description = nl_args[0].strip('"')
            language = nl_args[1] if len(nl_args) > 1 else "python"
            context = nl_args[2].strip('"') if len(nl_args) > 2 else None

            try:
                result = self.ai_reasoning_tool.natural_language_to_code(description, language, context)
                if result["success"]:
                    return f"Natural language to code result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in code generation: {result['error']}"
            except Exception as e:
                return f"Error in code generation: {e}"

        elif operation == "intent_recognition":
            # Recognize intent from natural language
            if len(args_parts) < 2:
                return "Error: No text specified for intent recognition."

            text = args_parts[1].strip('"')

            try:
                result = self.ai_reasoning_tool.intent_recognition(text)
                if result["success"]:
                    return f"Intent recognition result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in intent recognition: {result['error']}"
            except Exception as e:
                return f"Error in intent recognition: {e}"

        elif operation == "self_critique":
            # Perform self-critique on code
            if len(args_parts) < 2:
                return "Error: No code specified for self-critique."

            # Parse: "code" [language] [criteria]
            critique_args = args_parts[1].split(maxsplit=2)
            code = critique_args[0].strip('"')
            language = critique_args[1] if len(critique_args) > 1 else "python"
            criteria = critique_args[2].split(',') if len(critique_args) > 2 else None

            try:
                result = self.ai_reasoning_tool.self_critique(code, language, criteria)
                if result["success"]:
                    return f"Self-critique result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in self-critique: {result['error']}"
            except Exception as e:
                return f"Error in self-critique: {e}"

        elif operation == "chain_of_thought_reasoning":
            # Perform chain-of-thought reasoning
            if len(args_parts) < 2:
                return "Error: No problem specified for reasoning."

            # Parse: "problem" [steps]
            reasoning_args = args_parts[1].split(maxsplit=1)
            problem = reasoning_args[0].strip('"')
            steps = reasoning_args[1].split(',') if len(reasoning_args) > 1 else None

            try:
                result = self.ai_reasoning_tool.chain_of_thought_reasoning(problem, steps)
                if result["success"]:
                    return f"Chain-of-thought reasoning result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in reasoning: {result['error']}"
            except Exception as e:
                return f"Error in reasoning: {e}"

        elif operation == "smart_prefetching":
            # Predict and prefetch resources
            if len(args_parts) < 2:
                return "Error: No context specified for prefetching."

            # Parse: "context" [horizon]
            prefetch_args = args_parts[1].split(maxsplit=1)
            context = prefetch_args[0].strip('"')
            horizon = int(prefetch_args[1]) if len(prefetch_args) > 1 else 5

            try:
                result = self.ai_reasoning_tool.smart_prefetching(context, horizon)
                if result["success"]:
                    return f"Smart prefetching result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in prefetching: {result['error']}"
            except Exception as e:
                return f"Error in prefetching: {e}"

        elif operation == "background_task_prediction":
            # Predict background tasks
            if len(args_parts) < 2:
                return "Error: No current tasks specified for prediction."

            # Parse: "current_tasks" [system_state]
            task_args = args_parts[1].split(maxsplit=1)
            current_tasks = task_args[0].strip('"').split(',')
            system_state = json.loads(task_args[1]) if len(task_args) > 1 else None

            try:
                result = self.ai_reasoning_tool.background_task_prediction(current_tasks, system_state)
                if result["success"]:
                    return f"Background task prediction result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in task prediction: {result['error']}"
            except Exception as e:
                return f"Error in task prediction: {e}"

        elif operation == "predict_next_code_block":
            # Predict next code block
            if len(args_parts) < 2:
                return "Error: No current code specified for prediction."

            # Parse: "current_code" [language] [context]
            code_args = args_parts[1].split(maxsplit=2)
            current_code = code_args[0].strip('"')
            language = code_args[1] if len(code_args) > 1 else "python"
            context = code_args[2].strip('"') if len(code_args) > 2 else None

            try:
                result = self.ai_reasoning_tool.predict_next_code_block(current_code, language, context)
                if result["success"]:
                    return f"Code prediction result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in code prediction: {result['error']}"
            except Exception as e:
                return f"Error in code prediction: {e}"

        elif operation == "auto_complete":
            # Provide auto-completion suggestions
            if len(args_parts) < 2:
                return "Error: No partial code specified for auto-completion."

            # Parse: "partial_code" [language] [max_suggestions]
            complete_args = args_parts[1].split(maxsplit=2)
            partial_code = complete_args[0].strip('"')
            language = complete_args[1] if len(complete_args) > 1 else "python"
            max_suggestions = int(complete_args[2]) if len(complete_args) > 2 else 5

            try:
                result = self.ai_reasoning_tool.auto_complete(partial_code, language, max_suggestions)
                if result["success"]:
                    return f"Auto-completion result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in auto-completion: {result['error']}"
            except Exception as e:
                return f"Error in auto-completion: {e}"

        elif operation == "next_step_prediction":
            # Predict next workflow step
            if len(args_parts) < 2:
                return "Error: No current state specified for step prediction."

            # Parse: "current_state" "goal" [history]
            step_args = args_parts[1].split(maxsplit=2)
            if len(step_args) < 2:
                return "Error: Both current_state and goal required."

            current_state = step_args[0].strip('"')
            goal = step_args[1].strip('"')
            history = step_args[2].split(',') if len(step_args) > 2 else None

            try:
                result = self.ai_reasoning_tool.next_step_prediction(current_state, goal, history)
                if result["success"]:
                    return f"Next step prediction result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in step prediction: {result['error']}"
            except Exception as e:
                return f"Error in step prediction: {e}"

        else:
            return f"Error: Unknown AI reasoning operation: {operation}. Available operations: natural_language_to_code, intent_recognition, self_critique, chain_of_thought_reasoning, smart_prefetching, background_task_prediction, predict_next_code_block, auto_complete, next_step_prediction"

    def _execute_advanced_execution(self, args: str) -> str:
        """Execute an advanced execution operation.

        Args:
            args: The arguments for the advanced execution operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No advanced execution operation specified."

        operation = args_parts[0]

        if operation == "step_by_step_execution":
            # Execute a plan step by step
            if len(args_parts) < 2:
                return "Error: No execution plan specified."

            # Parse: plan_json [interactive]
            exec_args = args_parts[1].split(maxsplit=1)
            try:
                plan = json.loads(exec_args[0])
            except json.JSONDecodeError:
                return "Error: Invalid JSON format for execution plan."

            interactive = exec_args[1].lower() == "true" if len(exec_args) > 1 else False

            try:
                result = self.advanced_execution_tool.step_by_step_execution(plan, interactive)
                if result["success"]:
                    return f"Step-by-step execution result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in step-by-step execution: {result['error']}"
            except Exception as e:
                return f"Error in step-by-step execution: {e}"

        elif operation == "result_analysis":
            # Analyze execution results
            if len(args_parts) < 2:
                return "Error: No execution ID specified for analysis."

            # Parse: execution_id [analysis_type]
            analysis_args = args_parts[1].split()
            execution_id = analysis_args[0]
            analysis_type = analysis_args[1] if len(analysis_args) > 1 else "comprehensive"

            try:
                result = self.advanced_execution_tool.result_analysis(execution_id, analysis_type)
                if result["success"]:
                    return f"Result analysis:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in result analysis: {result['error']}"
            except Exception as e:
                return f"Error in result analysis: {e}"

        elif operation == "iterative_planning":
            # Perform iterative planning
            if len(args_parts) < 2:
                return "Error: No initial goal specified for iterative planning."

            # Parse: "initial_goal" [max_iterations] [adaptation_threshold]
            planning_args = args_parts[1].split(maxsplit=2)
            initial_goal = planning_args[0].strip('"')
            max_iterations = int(planning_args[1]) if len(planning_args) > 1 else 5
            adaptation_threshold = float(planning_args[2]) if len(planning_args) > 2 else 0.7

            try:
                result = self.advanced_execution_tool.iterative_planning(
                    initial_goal, max_iterations, adaptation_threshold
                )
                if result["success"]:
                    return f"Iterative planning result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in iterative planning: {result['error']}"
            except Exception as e:
                return f"Error in iterative planning: {e}"

        elif operation == "multi_threading_execution":
            # Execute tasks using multi-threading
            if len(args_parts) < 2:
                return "Error: No tasks specified for multi-threading execution."

            # Parse: tasks_json [max_workers]
            threading_args = args_parts[1].split(maxsplit=1)
            try:
                tasks = json.loads(threading_args[0])
            except json.JSONDecodeError:
                return "Error: Invalid JSON format for tasks."

            max_workers = int(threading_args[1]) if len(threading_args) > 1 else 4

            try:
                result = self.advanced_execution_tool.multi_threading_execution(tasks, max_workers)
                if result["success"]:
                    return f"Multi-threading execution result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in multi-threading execution: {result['error']}"
            except Exception as e:
                return f"Error in multi-threading execution: {e}"

        elif operation == "context_aware_auto_refactoring":
            # Perform context-aware auto-refactoring
            if len(args_parts) < 2:
                return "Error: No code files specified for refactoring."

            # Parse: "file1,file2,file3" "goal1,goal2"
            refactor_args = args_parts[1].split(maxsplit=1)
            code_files = refactor_args[0].strip('"').split(',')
            refactoring_goals = refactor_args[1].strip('"').split(',') if len(refactor_args) > 1 else ["readability"]

            try:
                result = self.advanced_execution_tool.context_aware_auto_refactoring(code_files, refactoring_goals)
                if result["success"]:
                    return f"Auto-refactoring result:\n\n{json.dumps(result, indent=2)}"
                else:
                    return f"Error in auto-refactoring: {result['error']}"
            except Exception as e:
                return f"Error in auto-refactoring: {e}"

        else:
            return f"Error: Unknown advanced execution operation: {operation}. Available operations: step_by_step_execution, result_analysis, iterative_planning, multi_threading_execution, context_aware_auto_refactoring"
