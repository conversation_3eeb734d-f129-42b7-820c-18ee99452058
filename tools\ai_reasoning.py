"""
Advanced AI Reasoning and NLP Tool for the AI Agent.
Implements comprehensive AI reasoning, natural language processing, and intelligent automation.
"""

import os
import re
import ast
import json
import time
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Set, Callable
import hashlib
import queue
import concurrent.futures

logger = logging.getLogger(__name__)

class AIReasoningTool:
    """Advanced tool for AI reasoning, NLP, and intelligent automation."""

    def __init__(self, workspace_dir: Path):
        """Initialize the AI reasoning tool.

        Args:
            workspace_dir: The workspace directory to use.
        """
        self.workspace_dir = workspace_dir
        self.history: List[Dict[str, Any]] = []
        
        # Reasoning and context storage
        self.reasoning_chains = {}
        self.context_cache = {}
        self.prediction_cache = {}
        self.intent_patterns = {}
        
        # Background processing
        self.background_tasks = queue.Queue()
        self.prediction_models = {}
        
        # Auto-completion and suggestions
        self.completion_cache = {}
        self.suggestion_patterns = {}
        
        # Initialize NLP patterns and models
        self._initialize_nlp_patterns()
        self._initialize_code_patterns()

    def _initialize_nlp_patterns(self):
        """Initialize natural language processing patterns."""
        self.intent_patterns = {
            'create': [
                r'create\s+(?:a\s+)?(\w+)',
                r'make\s+(?:a\s+)?(\w+)',
                r'build\s+(?:a\s+)?(\w+)',
                r'generate\s+(?:a\s+)?(\w+)'
            ],
            'modify': [
                r'(?:change|modify|update|edit)\s+(\w+)',
                r'fix\s+(?:the\s+)?(\w+)',
                r'improve\s+(?:the\s+)?(\w+)'
            ],
            'analyze': [
                r'(?:analyze|examine|check|review)\s+(\w+)',
                r'what\s+(?:is|are)\s+(\w+)',
                r'how\s+(?:does|do)\s+(\w+)'
            ],
            'debug': [
                r'(?:debug|troubleshoot|diagnose)\s+(\w+)',
                r'(?:error|issue|problem)\s+(?:in|with)\s+(\w+)',
                r'(?:not\s+working|broken|failing)\s+(\w+)'
            ],
            'optimize': [
                r'(?:optimize|improve|enhance)\s+(\w+)',
                r'make\s+(\w+)\s+(?:faster|better|more\s+efficient)',
                r'(?:performance|speed)\s+(?:of\s+)?(\w+)'
            ]
        }

    def _initialize_code_patterns(self):
        """Initialize code completion and suggestion patterns."""
        self.suggestion_patterns = {
            'python': {
                'imports': [
                    'import os', 'import sys', 'import json', 'import re',
                    'from typing import Dict, List, Optional, Any',
                    'import logging', 'import time', 'import datetime'
                ],
                'functions': [
                    'def __init__(self):', 'def __str__(self):', 'def __repr__(self):',
                    'def main():', 'def process():', 'def validate():'
                ],
                'patterns': [
                    r'if\s+__name__\s*==\s*["\']__main__["\']',
                    r'try:\s*\n.*\nexcept.*:',
                    r'with\s+open\(["\'].*["\'].*\)\s+as\s+\w+:'
                ]
            },
            'javascript': {
                'imports': [
                    'const fs = require("fs");',
                    'const path = require("path");',
                    'const express = require("express");'
                ],
                'functions': [
                    'function() {}', 'async function() {}', '() => {}',
                    'constructor() {}', 'render() {}'
                ],
                'patterns': [
                    r'console\.log\(',
                    r'\.then\(\s*.*\s*\)\.catch\(',
                    r'async\s+\w+\s*\('
                ]
            }
        }

    def natural_language_to_code(self, description: str, language: str = "python",
                                context: Optional[str] = None) -> Dict[str, Any]:
        """Convert natural language descriptions to code.

        Args:
            description: Natural language description of what to code.
            language: Target programming language.
            context: Additional context for code generation.

        Returns:
            Result dictionary with generated code.
        """
        try:
            self.history.append({
                "action": "natural_language_to_code",
                "description": description,
                "language": language,
                "context": context,
                "timestamp": time.time()
            })
            
            # Analyze the description to understand intent
            intent_analysis = self.intent_recognition(description)
            
            # Generate code based on intent and language
            generated_code = self._generate_code_from_intent(
                description, language, intent_analysis, context
            )
            
            # Add explanatory comments
            commented_code = self._add_code_comments(generated_code, language, description)
            
            result = {
                "success": True,
                "description": description,
                "language": language,
                "intent_analysis": intent_analysis,
                "generated_code": commented_code,
                "code_length": len(commented_code),
                "estimated_complexity": self._estimate_code_complexity(commented_code, language)
            }
            
            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result
            
        except Exception as e:
            error_msg = f"Failed to convert natural language to code: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in natural language to code: {e}")
            return {
                "success": False,
                "error": error_msg,
                "description": description
            }

    def intent_recognition(self, text: str) -> Dict[str, Any]:
        """Recognize intent from natural language text.

        Args:
            text: Natural language text to analyze.

        Returns:
            Result dictionary with recognized intent.
        """
        try:
            self.history.append({
                "action": "intent_recognition",
                "text": text,
                "timestamp": time.time()
            })
            
            text_lower = text.lower()
            recognized_intents = []
            confidence_scores = {}
            
            # Check against intent patterns
            for intent, patterns in self.intent_patterns.items():
                for pattern in patterns:
                    matches = re.findall(pattern, text_lower)
                    if matches:
                        recognized_intents.append(intent)
                        confidence_scores[intent] = confidence_scores.get(intent, 0) + len(matches)
            
            # Determine primary intent
            primary_intent = max(confidence_scores.keys(), key=confidence_scores.get) if confidence_scores else "unknown"
            
            # Extract entities (nouns, objects)
            entities = self._extract_entities(text)
            
            # Determine action type
            action_type = self._determine_action_type(text_lower)
            
            result = {
                "success": True,
                "text": text,
                "primary_intent": primary_intent,
                "all_intents": recognized_intents,
                "confidence_scores": confidence_scores,
                "entities": entities,
                "action_type": action_type,
                "complexity": "high" if len(recognized_intents) > 2 else "medium" if recognized_intents else "low"
            }
            
            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result
            
        except Exception as e:
            error_msg = f"Failed to recognize intent: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in intent recognition: {e}")
            return {
                "success": False,
                "error": error_msg,
                "text": text
            }

    def _extract_entities(self, text: str) -> List[str]:
        """Extract entities (nouns, objects) from text."""
        # Simple entity extraction using common patterns
        entities = []
        
        # Extract words that might be entities (capitalized words, technical terms)
        words = text.split()
        for word in words:
            clean_word = re.sub(r'[^\w]', '', word)
            if (len(clean_word) > 2 and 
                (clean_word[0].isupper() or 
                 clean_word in ['function', 'class', 'method', 'variable', 'file', 'database', 'api', 'server'])):
                entities.append(clean_word.lower())
        
        return list(set(entities))  # Remove duplicates

    def _determine_action_type(self, text: str) -> str:
        """Determine the type of action from text."""
        if any(word in text for word in ['create', 'make', 'build', 'generate', 'new']):
            return "creation"
        elif any(word in text for word in ['modify', 'change', 'update', 'edit', 'fix']):
            return "modification"
        elif any(word in text for word in ['delete', 'remove', 'drop', 'clear']):
            return "deletion"
        elif any(word in text for word in ['analyze', 'check', 'examine', 'review', 'test']):
            return "analysis"
        elif any(word in text for word in ['run', 'execute', 'start', 'launch']):
            return "execution"
        else:
            return "unknown"

    def _generate_code_from_intent(self, description: str, language: str, 
                                 intent_analysis: Dict[str, Any], context: Optional[str]) -> str:
        """Generate code based on intent analysis."""
        primary_intent = intent_analysis.get("primary_intent", "unknown")
        entities = intent_analysis.get("entities", [])
        action_type = intent_analysis.get("action_type", "unknown")
        
        if language == "python":
            return self._generate_python_code(description, primary_intent, entities, action_type, context)
        elif language == "javascript":
            return self._generate_javascript_code(description, primary_intent, entities, action_type, context)
        else:
            return f"# Code generation for {language} not yet implemented\n# Description: {description}"

    def _generate_python_code(self, description: str, intent: str, entities: List[str], 
                            action_type: str, context: Optional[str]) -> str:
        """Generate Python code based on intent."""
        code_parts = []
        
        # Add imports if needed
        if any(entity in ['file', 'json', 'data'] for entity in entities):
            code_parts.append("import json")
            code_parts.append("import os")
        
        if intent == "create":
            if "function" in entities:
                func_name = next((e for e in entities if e not in ['function']), 'new_function')
                code_parts.append(f"\ndef {func_name}():")
                code_parts.append(f'    """Generated function for: {description}"""')
                code_parts.append("    # TODO: Implement function logic")
                code_parts.append("    pass")
            
            elif "class" in entities:
                class_name = next((e.capitalize() for e in entities if e not in ['class']), 'NewClass')
                code_parts.append(f"\nclass {class_name}:")
                code_parts.append(f'    """Generated class for: {description}"""')
                code_parts.append("    ")
                code_parts.append("    def __init__(self):")
                code_parts.append("        # TODO: Initialize class attributes")
                code_parts.append("        pass")
        
        elif intent == "analyze":
            code_parts.append("\ndef analyze_data(data):")
            code_parts.append(f'    """Analyze data as requested: {description}"""')
            code_parts.append("    # TODO: Implement analysis logic")
            code_parts.append("    results = {}")
            code_parts.append("    return results")
        
        # Default fallback
        if not code_parts:
            code_parts.append(f"# Generated code for: {description}")
            code_parts.append("# TODO: Implement the requested functionality")
        
        return "\n".join(code_parts)

    def _generate_javascript_code(self, description: str, intent: str, entities: List[str], 
                                action_type: str, context: Optional[str]) -> str:
        """Generate JavaScript code based on intent."""
        code_parts = []
        
        if intent == "create":
            if "function" in entities:
                func_name = next((e for e in entities if e not in ['function']), 'newFunction')
                code_parts.append(f"function {func_name}() {{")
                code_parts.append(f"    // Generated function for: {description}")
                code_parts.append("    // TODO: Implement function logic")
                code_parts.append("}")
            
            elif "class" in entities:
                class_name = next((e.capitalize() for e in entities if e not in ['class']), 'NewClass')
                code_parts.append(f"class {class_name} {{")
                code_parts.append(f"    // Generated class for: {description}")
                code_parts.append("    constructor() {")
                code_parts.append("        // TODO: Initialize class properties")
                code_parts.append("    }")
                code_parts.append("}")
        
        # Default fallback
        if not code_parts:
            code_parts.append(f"// Generated code for: {description}")
            code_parts.append("// TODO: Implement the requested functionality")
        
        return "\n".join(code_parts)

    def _add_code_comments(self, code: str, language: str, description: str) -> str:
        """Add explanatory comments to generated code."""
        if language == "python":
            header = f'"""\nGenerated code for: {description}\nAuto-generated by AI Reasoning Tool\n"""\n\n'
        elif language == "javascript":
            header = f'/**\n * Generated code for: {description}\n * Auto-generated by AI Reasoning Tool\n */\n\n'
        else:
            header = f"// Generated code for: {description}\n// Auto-generated by AI Reasoning Tool\n\n"
        
        return header + code

    def _estimate_code_complexity(self, code: str, language: str) -> str:
        """Estimate the complexity of generated code."""
        lines = len(code.split('\n'))
        functions = len(re.findall(r'def\s+\w+|function\s+\w+|class\s+\w+', code))
        
        if lines > 50 or functions > 5:
            return "high"
        elif lines > 20 or functions > 2:
            return "medium"
        else:
            return "low"

    def self_critique(self, code: str, language: str = "python",
                     criteria: Optional[List[str]] = None) -> Dict[str, Any]:
        """Perform self-critique and improvement suggestions on code.

        Args:
            code: Code to critique.
            language: Programming language of the code.
            criteria: Specific criteria to evaluate (e.g., 'performance', 'readability').

        Returns:
            Result dictionary with critique and suggestions.
        """
        try:
            self.history.append({
                "action": "self_critique",
                "code_length": len(code),
                "language": language,
                "criteria": criteria,
                "timestamp": time.time()
            })

            if criteria is None:
                criteria = ['readability', 'performance', 'maintainability', 'security']

            critique_results = {}
            overall_score = 0
            total_criteria = len(criteria)

            for criterion in criteria:
                score, issues, suggestions = self._evaluate_criterion(code, language, criterion)
                critique_results[criterion] = {
                    "score": score,
                    "issues": issues,
                    "suggestions": suggestions
                }
                overall_score += score

            overall_score = overall_score / total_criteria if total_criteria > 0 else 0

            # Generate improvement recommendations
            improvements = self._generate_improvement_recommendations(critique_results, code, language)

            result = {
                "success": True,
                "language": language,
                "criteria": criteria,
                "overall_score": overall_score,
                "grade": self._score_to_grade(overall_score),
                "critique_results": critique_results,
                "improvements": improvements,
                "code_metrics": self._calculate_code_metrics(code, language)
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to perform self-critique: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in self-critique: {e}")
            return {
                "success": False,
                "error": error_msg
            }

    def _evaluate_criterion(self, code: str, language: str, criterion: str) -> Tuple[float, List[str], List[str]]:
        """Evaluate code against a specific criterion."""
        issues = []
        suggestions = []
        score = 8.0  # Default good score

        lines = code.split('\n')

        if criterion == 'readability':
            # Check line length
            long_lines = [i+1 for i, line in enumerate(lines) if len(line) > 120]
            if long_lines:
                issues.append(f"Long lines found at: {long_lines[:5]}")
                suggestions.append("Break long lines into multiple lines")
                score -= 1.0

            # Check for comments
            comment_ratio = len([line for line in lines if line.strip().startswith('#')]) / len(lines)
            if comment_ratio < 0.1:
                issues.append("Low comment ratio")
                suggestions.append("Add more explanatory comments")
                score -= 0.5

        elif criterion == 'performance':
            # Check for potential performance issues
            if language == 'python':
                if 'for' in code and 'append' in code:
                    issues.append("Potential inefficient list building")
                    suggestions.append("Consider using list comprehensions")
                    score -= 0.5

                if re.search(r'\.keys\(\)\s*in', code):
                    issues.append("Inefficient dictionary key checking")
                    suggestions.append("Use 'in dict' instead of 'in dict.keys()'")
                    score -= 0.5

        elif criterion == 'maintainability':
            # Check function length
            if language == 'python':
                functions = re.findall(r'def\s+\w+.*?(?=\ndef|\nclass|\Z)', code, re.DOTALL)
                for func in functions:
                    func_lines = len(func.split('\n'))
                    if func_lines > 50:
                        issues.append("Very long function detected")
                        suggestions.append("Break down large functions into smaller ones")
                        score -= 1.0

        elif criterion == 'security':
            # Basic security checks
            if 'eval(' in code:
                issues.append("Use of eval() detected")
                suggestions.append("Avoid using eval() - use safer alternatives")
                score -= 2.0

            if 'exec(' in code:
                issues.append("Use of exec() detected")
                suggestions.append("Avoid using exec() - use safer alternatives")
                score -= 2.0

        return max(0.0, score), issues, suggestions

    def _score_to_grade(self, score: float) -> str:
        """Convert numeric score to letter grade."""
        if score >= 9.0:
            return "A+"
        elif score >= 8.5:
            return "A"
        elif score >= 8.0:
            return "A-"
        elif score >= 7.5:
            return "B+"
        elif score >= 7.0:
            return "B"
        elif score >= 6.5:
            return "B-"
        elif score >= 6.0:
            return "C+"
        elif score >= 5.5:
            return "C"
        elif score >= 5.0:
            return "C-"
        else:
            return "D"

    def _generate_improvement_recommendations(self, critique_results: Dict[str, Any],
                                           code: str, language: str) -> List[str]:
        """Generate overall improvement recommendations."""
        recommendations = []

        # Collect all suggestions
        all_suggestions = []
        for criterion, results in critique_results.items():
            all_suggestions.extend(results.get("suggestions", []))

        # Prioritize suggestions
        if all_suggestions:
            recommendations.extend(all_suggestions[:5])  # Top 5 suggestions

        # Add general recommendations based on language
        if language == 'python':
            recommendations.append("Follow PEP 8 style guidelines")
            recommendations.append("Add type hints for better code documentation")
        elif language == 'javascript':
            recommendations.append("Use ESLint for consistent code style")
            recommendations.append("Consider using TypeScript for better type safety")

        return recommendations

    def _calculate_code_metrics(self, code: str, language: str) -> Dict[str, Any]:
        """Calculate various code metrics."""
        lines = code.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]

        metrics = {
            "total_lines": len(lines),
            "non_empty_lines": len(non_empty_lines),
            "comment_lines": len([line for line in lines if line.strip().startswith('#')]),
            "average_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0
        }

        if language == 'python':
            metrics.update({
                "functions": len(re.findall(r'def\s+\w+', code)),
                "classes": len(re.findall(r'class\s+\w+', code)),
                "imports": len(re.findall(r'(?:^|\n)(?:import|from)\s+', code))
            })
        elif language == 'javascript':
            metrics.update({
                "functions": len(re.findall(r'function\s+\w+|=>\s*{', code)),
                "classes": len(re.findall(r'class\s+\w+', code)),
                "requires": len(re.findall(r'require\s*\(', code))
            })

        return metrics

    def chain_of_thought_reasoning(self, problem: str, steps: Optional[List[str]] = None) -> Dict[str, Any]:
        """Implement chain-of-thought reasoning for complex problems.

        Args:
            problem: The problem to solve using chain-of-thought reasoning.
            steps: Optional predefined reasoning steps.

        Returns:
            Result dictionary with reasoning chain and solution.
        """
        try:
            self.history.append({
                "action": "chain_of_thought_reasoning",
                "problem": problem,
                "predefined_steps": steps,
                "timestamp": time.time()
            })

            # Generate reasoning chain ID
            chain_id = hashlib.md5(f"{problem}{time.time()}".encode()).hexdigest()[:8]

            # Break down the problem into steps
            if steps is None:
                steps = self._generate_reasoning_steps(problem)

            reasoning_chain = []
            current_context = problem

            for i, step in enumerate(steps, 1):
                step_result = self._execute_reasoning_step(step, current_context, i)
                reasoning_chain.append(step_result)

                # Update context with step result
                current_context += f"\nStep {i} result: {step_result.get('conclusion', '')}"

            # Generate final solution
            final_solution = self._synthesize_solution(problem, reasoning_chain)

            # Store reasoning chain
            self.reasoning_chains[chain_id] = {
                "problem": problem,
                "steps": steps,
                "reasoning_chain": reasoning_chain,
                "final_solution": final_solution,
                "created_at": time.time()
            }

            result = {
                "success": True,
                "chain_id": chain_id,
                "problem": problem,
                "total_steps": len(steps),
                "reasoning_chain": reasoning_chain,
                "final_solution": final_solution,
                "confidence": self._calculate_reasoning_confidence(reasoning_chain)
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to perform chain-of-thought reasoning: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in chain-of-thought reasoning: {e}")
            return {
                "success": False,
                "error": error_msg,
                "problem": problem
            }

    def _generate_reasoning_steps(self, problem: str) -> List[str]:
        """Generate reasoning steps for a given problem."""
        # Analyze problem type and generate appropriate steps
        problem_lower = problem.lower()

        if any(word in problem_lower for word in ['debug', 'error', 'fix', 'issue']):
            return [
                "Identify the specific error or issue",
                "Analyze the root cause",
                "Consider possible solutions",
                "Evaluate solution feasibility",
                "Implement the best solution"
            ]
        elif any(word in problem_lower for word in ['design', 'architecture', 'plan']):
            return [
                "Define requirements and constraints",
                "Identify key components and relationships",
                "Design the overall architecture",
                "Consider scalability and maintainability",
                "Validate the design"
            ]
        elif any(word in problem_lower for word in ['optimize', 'improve', 'performance']):
            return [
                "Identify performance bottlenecks",
                "Analyze current implementation",
                "Research optimization techniques",
                "Implement optimizations",
                "Measure and validate improvements"
            ]
        else:
            return [
                "Understand the problem clearly",
                "Break down into smaller sub-problems",
                "Analyze each sub-problem",
                "Develop solution approach",
                "Synthesize final solution"
            ]

    def _execute_reasoning_step(self, step: str, context: str, step_number: int) -> Dict[str, Any]:
        """Execute a single reasoning step."""
        return {
            "step_number": step_number,
            "step_description": step,
            "analysis": f"Analyzing: {step}",
            "conclusion": f"Completed step {step_number}: {step}",
            "confidence": 0.8,  # Default confidence
            "timestamp": time.time()
        }

    def _synthesize_solution(self, problem: str, reasoning_chain: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Synthesize final solution from reasoning chain."""
        return {
            "solution_summary": f"Solution for: {problem}",
            "key_insights": [step.get("conclusion", "") for step in reasoning_chain],
            "recommended_actions": ["Implement the proposed solution", "Test thoroughly", "Monitor results"],
            "confidence": self._calculate_reasoning_confidence(reasoning_chain)
        }

    def _calculate_reasoning_confidence(self, reasoning_chain: List[Dict[str, Any]]) -> float:
        """Calculate overall confidence in the reasoning chain."""
        if not reasoning_chain:
            return 0.0

        confidences = [step.get("confidence", 0.5) for step in reasoning_chain]
        return sum(confidences) / len(confidences)

    def smart_prefetching(self, current_context: str, prediction_horizon: int = 5) -> Dict[str, Any]:
        """Predict and prefetch likely needed resources/data.

        Args:
            current_context: Current context or state.
            prediction_horizon: Number of steps ahead to predict.

        Returns:
            Result dictionary with prefetching predictions.
        """
        try:
            self.history.append({
                "action": "smart_prefetching",
                "current_context": current_context,
                "prediction_horizon": prediction_horizon,
                "timestamp": time.time()
            })

            # Analyze current context to predict future needs
            context_analysis = self._analyze_context_for_prediction(current_context)

            # Generate predictions for different resource types
            predictions = {
                "files": self._predict_file_needs(context_analysis, prediction_horizon),
                "tools": self._predict_tool_needs(context_analysis, prediction_horizon),
                "data": self._predict_data_needs(context_analysis, prediction_horizon),
                "actions": self._predict_action_sequence(context_analysis, prediction_horizon)
            }

            # Calculate confidence scores
            confidence_scores = {}
            for category, items in predictions.items():
                if items:
                    confidence_scores[category] = sum(item.get("confidence", 0.5) for item in items) / len(items)
                else:
                    confidence_scores[category] = 0.0

            result = {
                "success": True,
                "current_context": current_context,
                "prediction_horizon": prediction_horizon,
                "context_analysis": context_analysis,
                "predictions": predictions,
                "confidence_scores": confidence_scores,
                "total_predictions": sum(len(items) for items in predictions.values())
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to perform smart prefetching: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in smart prefetching: {e}")
            return {
                "success": False,
                "error": error_msg,
                "current_context": current_context
            }

    def _analyze_context_for_prediction(self, context: str) -> Dict[str, Any]:
        """Analyze context to understand current state and predict future needs."""
        analysis = {
            "activity_type": "unknown",
            "technologies": [],
            "current_phase": "unknown",
            "complexity": "medium",
            "patterns": []
        }

        context_lower = context.lower()

        # Detect activity type
        if any(word in context_lower for word in ['debug', 'error', 'fix', 'issue']):
            analysis["activity_type"] = "debugging"
        elif any(word in context_lower for word in ['test', 'testing', 'coverage']):
            analysis["activity_type"] = "testing"
        elif any(word in context_lower for word in ['develop', 'code', 'implement']):
            analysis["activity_type"] = "development"
        elif any(word in context_lower for word in ['deploy', 'release', 'production']):
            analysis["activity_type"] = "deployment"

        # Detect technologies
        tech_keywords = {
            "python": ["python", "django", "flask", "fastapi"],
            "javascript": ["javascript", "node", "react", "vue"],
            "database": ["database", "sql", "mongodb", "postgresql"],
            "web": ["web", "html", "css", "api", "rest"]
        }

        for tech, keywords in tech_keywords.items():
            if any(keyword in context_lower for keyword in keywords):
                analysis["technologies"].append(tech)

        return analysis

    def _predict_file_needs(self, analysis: Dict[str, Any], horizon: int) -> List[Dict[str, Any]]:
        """Predict which files might be needed."""
        predictions = []
        activity_type = analysis.get("activity_type", "unknown")
        technologies = analysis.get("technologies", [])

        if activity_type == "debugging":
            predictions.extend([
                {"file": "logs/error.log", "confidence": 0.8, "reason": "Error logs for debugging"},
                {"file": "config/settings.py", "confidence": 0.6, "reason": "Configuration files often involved in debugging"}
            ])

        elif activity_type == "testing":
            predictions.extend([
                {"file": "tests/", "confidence": 0.9, "reason": "Test files for testing activity"},
                {"file": "pytest.ini", "confidence": 0.7, "reason": "Test configuration"}
            ])

        if "python" in technologies:
            predictions.extend([
                {"file": "requirements.txt", "confidence": 0.7, "reason": "Python dependencies"},
                {"file": "setup.py", "confidence": 0.5, "reason": "Python package setup"}
            ])

        return predictions[:horizon]

    def _predict_tool_needs(self, analysis: Dict[str, Any], horizon: int) -> List[Dict[str, Any]]:
        """Predict which tools might be needed."""
        predictions = []
        activity_type = analysis.get("activity_type", "unknown")

        if activity_type == "debugging":
            predictions.extend([
                {"tool": "test", "operation": "autonomous_debugger", "confidence": 0.9},
                {"tool": "file", "operation": "get_errors", "confidence": 0.8},
                {"tool": "shell", "operation": "get_terminal_output", "confidence": 0.7}
            ])

        elif activity_type == "development":
            predictions.extend([
                {"tool": "file", "operation": "create_file", "confidence": 0.8},
                {"tool": "code", "operation": "execute", "confidence": 0.7},
                {"tool": "test", "operation": "run_tests", "confidence": 0.6}
            ])

        return predictions[:horizon]

    def _predict_data_needs(self, analysis: Dict[str, Any], horizon: int) -> List[Dict[str, Any]]:
        """Predict what data might be needed."""
        predictions = []
        technologies = analysis.get("technologies", [])

        if "database" in technologies:
            predictions.extend([
                {"data": "database_schema", "confidence": 0.8, "reason": "Database operations likely"},
                {"data": "connection_config", "confidence": 0.7, "reason": "Database connection needed"}
            ])

        if "web" in technologies:
            predictions.extend([
                {"data": "api_endpoints", "confidence": 0.7, "reason": "Web API development"},
                {"data": "request_examples", "confidence": 0.6, "reason": "API testing data"}
            ])

        return predictions[:horizon]

    def _predict_action_sequence(self, analysis: Dict[str, Any], horizon: int) -> List[Dict[str, Any]]:
        """Predict likely sequence of actions."""
        predictions = []
        activity_type = analysis.get("activity_type", "unknown")

        if activity_type == "debugging":
            predictions.extend([
                {"action": "reproduce_error", "confidence": 0.9, "step": 1},
                {"action": "analyze_logs", "confidence": 0.8, "step": 2},
                {"action": "identify_root_cause", "confidence": 0.7, "step": 3},
                {"action": "implement_fix", "confidence": 0.6, "step": 4},
                {"action": "test_fix", "confidence": 0.8, "step": 5}
            ])

        elif activity_type == "development":
            predictions.extend([
                {"action": "design_solution", "confidence": 0.8, "step": 1},
                {"action": "implement_code", "confidence": 0.9, "step": 2},
                {"action": "write_tests", "confidence": 0.7, "step": 3},
                {"action": "run_tests", "confidence": 0.8, "step": 4},
                {"action": "refactor_code", "confidence": 0.6, "step": 5}
            ])

        return predictions[:horizon]

    def background_task_prediction(self, current_tasks: List[str],
                                 system_state: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Predict background tasks that should be running.

        Args:
            current_tasks: List of currently running tasks.
            system_state: Current system state information.

        Returns:
            Result dictionary with background task predictions.
        """
        try:
            self.history.append({
                "action": "background_task_prediction",
                "current_tasks": current_tasks,
                "system_state": system_state,
                "timestamp": time.time()
            })

            if system_state is None:
                system_state = {}

            # Analyze current tasks and system state
            task_analysis = self._analyze_current_tasks(current_tasks)

            # Predict needed background tasks
            predicted_tasks = self._predict_background_tasks(task_analysis, system_state)

            # Identify missing tasks
            missing_tasks = [task for task in predicted_tasks
                           if task["task_name"] not in current_tasks]

            # Identify unnecessary tasks
            unnecessary_tasks = [task for task in current_tasks
                               if not self._is_task_necessary(task, predicted_tasks)]

            result = {
                "success": True,
                "current_tasks": current_tasks,
                "predicted_tasks": predicted_tasks,
                "missing_tasks": missing_tasks,
                "unnecessary_tasks": unnecessary_tasks,
                "task_analysis": task_analysis,
                "recommendations": self._generate_task_recommendations(missing_tasks, unnecessary_tasks)
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to predict background tasks: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in background task prediction: {e}")
            return {
                "success": False,
                "error": error_msg,
                "current_tasks": current_tasks
            }

    def _analyze_current_tasks(self, tasks: List[str]) -> Dict[str, Any]:
        """Analyze current tasks to understand system activity."""
        analysis = {
            "task_types": {},
            "resource_usage": "unknown",
            "activity_level": "medium",
            "patterns": []
        }

        # Categorize tasks
        for task in tasks:
            task_lower = task.lower()
            if any(word in task_lower for word in ['server', 'service', 'daemon']):
                analysis["task_types"]["service"] = analysis["task_types"].get("service", 0) + 1
            elif any(word in task_lower for word in ['build', 'compile', 'test']):
                analysis["task_types"]["build"] = analysis["task_types"].get("build", 0) + 1
            elif any(word in task_lower for word in ['monitor', 'watch', 'log']):
                analysis["task_types"]["monitoring"] = analysis["task_types"].get("monitoring", 0) + 1

        # Determine activity level
        if len(tasks) > 10:
            analysis["activity_level"] = "high"
        elif len(tasks) < 3:
            analysis["activity_level"] = "low"

        return analysis

    def _predict_background_tasks(self, task_analysis: Dict[str, Any],
                                system_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Predict what background tasks should be running."""
        predicted_tasks = []

        # Always useful background tasks
        predicted_tasks.extend([
            {"task_name": "system_monitor", "priority": "medium", "confidence": 0.7},
            {"task_name": "log_rotation", "priority": "low", "confidence": 0.6}
        ])

        # Development-specific tasks
        if task_analysis.get("task_types", {}).get("build", 0) > 0:
            predicted_tasks.extend([
                {"task_name": "file_watcher", "priority": "high", "confidence": 0.9},
                {"task_name": "auto_test_runner", "priority": "medium", "confidence": 0.8}
            ])

        # Service-specific tasks
        if task_analysis.get("task_types", {}).get("service", 0) > 0:
            predicted_tasks.extend([
                {"task_name": "health_checker", "priority": "high", "confidence": 0.9},
                {"task_name": "performance_monitor", "priority": "medium", "confidence": 0.7}
            ])

        return predicted_tasks

    def _is_task_necessary(self, task: str, predicted_tasks: List[Dict[str, Any]]) -> bool:
        """Check if a current task is necessary based on predictions."""
        predicted_names = [pt["task_name"] for pt in predicted_tasks]
        return any(name in task.lower() for name in predicted_names)

    def _generate_task_recommendations(self, missing_tasks: List[Dict[str, Any]],
                                     unnecessary_tasks: List[str]) -> List[str]:
        """Generate recommendations for task management."""
        recommendations = []

        if missing_tasks:
            high_priority_missing = [task for task in missing_tasks if task.get("priority") == "high"]
            if high_priority_missing:
                recommendations.append(f"Start high-priority tasks: {[t['task_name'] for t in high_priority_missing]}")

        if unnecessary_tasks:
            recommendations.append(f"Consider stopping unnecessary tasks: {unnecessary_tasks}")

        if not missing_tasks and not unnecessary_tasks:
            recommendations.append("Current task configuration appears optimal")

        return recommendations

    def predict_next_code_block(self, current_code: str, language: str = "python",
                               context: Optional[str] = None) -> Dict[str, Any]:
        """Predict what code block should come next.

        Args:
            current_code: The current code context.
            language: Programming language.
            context: Additional context for prediction.

        Returns:
            Result dictionary with predicted code block.
        """
        try:
            self.history.append({
                "action": "predict_next_code_block",
                "current_code_length": len(current_code),
                "language": language,
                "context": context,
                "timestamp": time.time()
            })

            # Analyze current code structure
            code_analysis = self._analyze_code_structure(current_code, language)

            # Predict next logical code block
            prediction = self._generate_code_prediction(current_code, language, code_analysis, context)

            result = {
                "success": True,
                "language": language,
                "code_analysis": code_analysis,
                "predicted_code": prediction["code"],
                "prediction_type": prediction["type"],
                "confidence": prediction["confidence"],
                "reasoning": prediction["reasoning"]
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to predict next code block: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in code prediction: {e}")
            return {
                "success": False,
                "error": error_msg
            }

    def _analyze_code_structure(self, code: str, language: str) -> Dict[str, Any]:
        """Analyze the structure of current code."""
        analysis = {
            "incomplete_blocks": [],
            "last_statement": "",
            "indentation_level": 0,
            "context_type": "unknown",
            "missing_elements": []
        }

        lines = code.split('\n')
        if not lines:
            return analysis

        last_line = lines[-1] if lines else ""
        analysis["last_statement"] = last_line.strip()
        analysis["indentation_level"] = len(last_line) - len(last_line.lstrip())

        if language == "python":
            # Check for incomplete blocks
            if last_line.strip().endswith(':'):
                analysis["incomplete_blocks"].append("block_definition")

            # Check for incomplete try/except
            if any('try:' in line for line in lines[-5:]):
                if not any('except' in line for line in lines[-5:]):
                    analysis["missing_elements"].append("except_block")

            # Check for incomplete if/else
            if any(line.strip().startswith('if ') and line.strip().endswith(':') for line in lines[-3:]):
                if not any('else:' in line for line in lines[-3:]):
                    analysis["missing_elements"].append("else_block")

        return analysis

    def _generate_code_prediction(self, current_code: str, language: str,
                                analysis: Dict[str, Any], context: Optional[str]) -> Dict[str, Any]:
        """Generate prediction for next code block."""
        prediction = {
            "code": "",
            "type": "unknown",
            "confidence": 0.5,
            "reasoning": ""
        }

        if language == "python":
            if "block_definition" in analysis.get("incomplete_blocks", []):
                # Need to add block content
                indent = " " * (analysis.get("indentation_level", 0) + 4)
                prediction.update({
                    "code": f"{indent}# TODO: Implement block logic\n{indent}pass",
                    "type": "block_implementation",
                    "confidence": 0.9,
                    "reasoning": "Incomplete block detected, adding placeholder implementation"
                })

            elif "except_block" in analysis.get("missing_elements", []):
                # Need to add except block
                indent = " " * analysis.get("indentation_level", 0)
                prediction.update({
                    "code": f"{indent}except Exception as e:\n{indent}    # Handle exception\n{indent}    logger.error(f'Error: {{e}}')",
                    "type": "exception_handling",
                    "confidence": 0.8,
                    "reasoning": "Try block without except detected"
                })

            elif analysis.get("last_statement", "").strip().startswith("def "):
                # Function definition, predict docstring and basic structure
                indent = " " * (analysis.get("indentation_level", 0) + 4)
                prediction.update({
                    "code": f'{indent}"""Function docstring."""\n{indent}# TODO: Implement function logic\n{indent}pass',
                    "type": "function_implementation",
                    "confidence": 0.7,
                    "reasoning": "Function definition detected, adding docstring and placeholder"
                })

        return prediction

    def auto_complete(self, partial_code: str, language: str = "python",
                     max_suggestions: int = 5) -> Dict[str, Any]:
        """Provide intelligent auto-completion suggestions.

        Args:
            partial_code: Partial code to complete.
            language: Programming language.
            max_suggestions: Maximum number of suggestions.

        Returns:
            Result dictionary with completion suggestions.
        """
        try:
            self.history.append({
                "action": "auto_complete",
                "partial_code": partial_code,
                "language": language,
                "max_suggestions": max_suggestions,
                "timestamp": time.time()
            })

            # Analyze partial code
            completion_context = self._analyze_completion_context(partial_code, language)

            # Generate suggestions
            suggestions = self._generate_completion_suggestions(
                partial_code, language, completion_context, max_suggestions
            )

            result = {
                "success": True,
                "partial_code": partial_code,
                "language": language,
                "completion_context": completion_context,
                "suggestions": suggestions,
                "total_suggestions": len(suggestions)
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to provide auto-completion: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in auto-completion: {e}")
            return {
                "success": False,
                "error": error_msg,
                "partial_code": partial_code
            }

    def _analyze_completion_context(self, partial_code: str, language: str) -> Dict[str, Any]:
        """Analyze context for auto-completion."""
        context = {
            "completion_type": "unknown",
            "prefix": "",
            "scope": "global",
            "available_symbols": []
        }

        # Get the last word/token being typed
        words = partial_code.split()
        if words:
            context["prefix"] = words[-1]

        # Determine completion type
        if partial_code.endswith('.'):
            context["completion_type"] = "method_or_attribute"
        elif 'import ' in partial_code and not partial_code.strip().endswith('import'):
            context["completion_type"] = "module_name"
        elif partial_code.strip().startswith('def '):
            context["completion_type"] = "function_definition"
        elif partial_code.strip().startswith('class '):
            context["completion_type"] = "class_definition"
        else:
            context["completion_type"] = "general"

        return context

    def _generate_completion_suggestions(self, partial_code: str, language: str,
                                       context: Dict[str, Any], max_suggestions: int) -> List[Dict[str, Any]]:
        """Generate auto-completion suggestions."""
        suggestions = []
        completion_type = context.get("completion_type", "unknown")
        prefix = context.get("prefix", "")

        if language == "python":
            if completion_type == "method_or_attribute":
                # Common methods and attributes
                common_methods = [
                    {"completion": "append()", "type": "method", "confidence": 0.8},
                    {"completion": "extend()", "type": "method", "confidence": 0.7},
                    {"completion": "remove()", "type": "method", "confidence": 0.7},
                    {"completion": "split()", "type": "method", "confidence": 0.8},
                    {"completion": "join()", "type": "method", "confidence": 0.7},
                    {"completion": "strip()", "type": "method", "confidence": 0.8}
                ]
                suggestions.extend(common_methods)

            elif completion_type == "module_name":
                # Common modules
                common_modules = [
                    {"completion": "os", "type": "module", "confidence": 0.9},
                    {"completion": "sys", "type": "module", "confidence": 0.8},
                    {"completion": "json", "type": "module", "confidence": 0.8},
                    {"completion": "re", "type": "module", "confidence": 0.7},
                    {"completion": "time", "type": "module", "confidence": 0.7},
                    {"completion": "datetime", "type": "module", "confidence": 0.7}
                ]
                suggestions.extend(common_modules)

            elif completion_type == "general":
                # General Python keywords and functions
                keywords = [
                    {"completion": "if", "type": "keyword", "confidence": 0.8},
                    {"completion": "for", "type": "keyword", "confidence": 0.8},
                    {"completion": "while", "type": "keyword", "confidence": 0.7},
                    {"completion": "try", "type": "keyword", "confidence": 0.7},
                    {"completion": "def", "type": "keyword", "confidence": 0.8},
                    {"completion": "class", "type": "keyword", "confidence": 0.7}
                ]
                suggestions.extend(keywords)

        # Filter by prefix if provided
        if prefix:
            suggestions = [s for s in suggestions if s["completion"].startswith(prefix)]

        # Sort by confidence and limit
        suggestions.sort(key=lambda x: x["confidence"], reverse=True)
        return suggestions[:max_suggestions]

    def next_step_prediction(self, current_state: str, goal: str,
                           history: Optional[List[str]] = None) -> Dict[str, Any]:
        """Predict the next logical step in a workflow.

        Args:
            current_state: Current state description.
            goal: Target goal description.
            history: Optional history of previous steps.

        Returns:
            Result dictionary with next step prediction.
        """
        try:
            self.history.append({
                "action": "next_step_prediction",
                "current_state": current_state,
                "goal": goal,
                "history": history,
                "timestamp": time.time()
            })

            # Analyze current state and goal
            state_analysis = self._analyze_workflow_state(current_state, goal, history)

            # Predict next steps
            next_steps = self._predict_workflow_steps(state_analysis, goal)

            # Select best next step
            best_step = self._select_best_next_step(next_steps, state_analysis)

            result = {
                "success": True,
                "current_state": current_state,
                "goal": goal,
                "state_analysis": state_analysis,
                "predicted_steps": next_steps,
                "recommended_step": best_step,
                "confidence": best_step.get("confidence", 0.5) if best_step else 0.0
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to predict next step: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in next step prediction: {e}")
            return {
                "success": False,
                "error": error_msg,
                "current_state": current_state
            }

    def _analyze_workflow_state(self, current_state: str, goal: str,
                              history: Optional[List[str]]) -> Dict[str, Any]:
        """Analyze the current workflow state."""
        analysis = {
            "progress": 0.0,
            "phase": "unknown",
            "blockers": [],
            "completed_actions": history or [],
            "remaining_effort": "unknown"
        }

        # Estimate progress based on state and goal similarity
        state_words = set(current_state.lower().split())
        goal_words = set(goal.lower().split())

        if goal_words:
            overlap = len(state_words.intersection(goal_words))
            analysis["progress"] = min(overlap / len(goal_words), 1.0)

        # Determine phase
        if analysis["progress"] < 0.3:
            analysis["phase"] = "initial"
        elif analysis["progress"] < 0.7:
            analysis["phase"] = "development"
        else:
            analysis["phase"] = "completion"

        return analysis

    def _predict_workflow_steps(self, state_analysis: Dict[str, Any], goal: str) -> List[Dict[str, Any]]:
        """Predict possible next workflow steps."""
        steps = []
        phase = state_analysis.get("phase", "unknown")

        if phase == "initial":
            steps.extend([
                {"step": "Define requirements", "confidence": 0.9, "priority": "high"},
                {"step": "Set up environment", "confidence": 0.8, "priority": "high"},
                {"step": "Create project structure", "confidence": 0.7, "priority": "medium"}
            ])
        elif phase == "development":
            steps.extend([
                {"step": "Implement core functionality", "confidence": 0.9, "priority": "high"},
                {"step": "Write tests", "confidence": 0.8, "priority": "medium"},
                {"step": "Debug issues", "confidence": 0.7, "priority": "medium"}
            ])
        elif phase == "completion":
            steps.extend([
                {"step": "Final testing", "confidence": 0.9, "priority": "high"},
                {"step": "Documentation", "confidence": 0.7, "priority": "medium"},
                {"step": "Deployment preparation", "confidence": 0.8, "priority": "high"}
            ])

        return steps

    def _select_best_next_step(self, steps: List[Dict[str, Any]],
                             state_analysis: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Select the best next step from predictions."""
        if not steps:
            return None

        # Sort by priority and confidence
        priority_order = {"high": 3, "medium": 2, "low": 1}

        def step_score(step):
            priority_score = priority_order.get(step.get("priority", "medium"), 2)
            confidence_score = step.get("confidence", 0.5)
            return priority_score * confidence_score

        best_step = max(steps, key=step_score)
        return best_step

    def get_history(self) -> List[Dict[str, Any]]:
        """Get the AI reasoning tool operation history."""
        return self.history
