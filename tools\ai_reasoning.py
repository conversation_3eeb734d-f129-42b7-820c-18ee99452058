"""
Advanced AI Reasoning and NLP Tool for the AI Agent.
Implements comprehensive AI reasoning, natural language processing, and intelligent automation.
"""

import os
import re
import ast
import json
import time
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Set, Callable
import hashlib
import queue
import concurrent.futures

logger = logging.getLogger(__name__)

class AIReasoningTool:
    """Advanced tool for AI reasoning, NLP, and intelligent automation."""

    def __init__(self, workspace_dir: Path):
        """Initialize the AI reasoning tool.

        Args:
            workspace_dir: The workspace directory to use.
        """
        self.workspace_dir = workspace_dir
        self.history: List[Dict[str, Any]] = []
        
        # Reasoning and context storage
        self.reasoning_chains = {}
        self.context_cache = {}
        self.prediction_cache = {}
        self.intent_patterns = {}
        
        # Background processing
        self.background_tasks = queue.Queue()
        self.prediction_models = {}
        
        # Auto-completion and suggestions
        self.completion_cache = {}
        self.suggestion_patterns = {}
        
        # Initialize NLP patterns and models
        self._initialize_nlp_patterns()
        self._initialize_code_patterns()

    def _initialize_nlp_patterns(self):
        """Initialize natural language processing patterns."""
        self.intent_patterns = {
            'create': [
                r'create\s+(?:a\s+)?(\w+)',
                r'make\s+(?:a\s+)?(\w+)',
                r'build\s+(?:a\s+)?(\w+)',
                r'generate\s+(?:a\s+)?(\w+)'
            ],
            'modify': [
                r'(?:change|modify|update|edit)\s+(\w+)',
                r'fix\s+(?:the\s+)?(\w+)',
                r'improve\s+(?:the\s+)?(\w+)'
            ],
            'analyze': [
                r'(?:analyze|examine|check|review)\s+(\w+)',
                r'what\s+(?:is|are)\s+(\w+)',
                r'how\s+(?:does|do)\s+(\w+)'
            ],
            'debug': [
                r'(?:debug|troubleshoot|diagnose)\s+(\w+)',
                r'(?:error|issue|problem)\s+(?:in|with)\s+(\w+)',
                r'(?:not\s+working|broken|failing)\s+(\w+)'
            ],
            'optimize': [
                r'(?:optimize|improve|enhance)\s+(\w+)',
                r'make\s+(\w+)\s+(?:faster|better|more\s+efficient)',
                r'(?:performance|speed)\s+(?:of\s+)?(\w+)'
            ]
        }

    def _initialize_code_patterns(self):
        """Initialize code completion and suggestion patterns."""
        self.suggestion_patterns = {
            'python': {
                'imports': [
                    'import os', 'import sys', 'import json', 'import re',
                    'from typing import Dict, List, Optional, Any',
                    'import logging', 'import time', 'import datetime'
                ],
                'functions': [
                    'def __init__(self):', 'def __str__(self):', 'def __repr__(self):',
                    'def main():', 'def process():', 'def validate():'
                ],
                'patterns': [
                    r'if\s+__name__\s*==\s*["\']__main__["\']',
                    r'try:\s*\n.*\nexcept.*:',
                    r'with\s+open\(["\'].*["\'].*\)\s+as\s+\w+:'
                ]
            },
            'javascript': {
                'imports': [
                    'const fs = require("fs");',
                    'const path = require("path");',
                    'const express = require("express");'
                ],
                'functions': [
                    'function() {}', 'async function() {}', '() => {}',
                    'constructor() {}', 'render() {}'
                ],
                'patterns': [
                    r'console\.log\(',
                    r'\.then\(\s*.*\s*\)\.catch\(',
                    r'async\s+\w+\s*\('
                ]
            }
        }

    def natural_language_to_code(self, description: str, language: str = "python",
                                context: Optional[str] = None) -> Dict[str, Any]:
        """Convert natural language descriptions to code.

        Args:
            description: Natural language description of what to code.
            language: Target programming language.
            context: Additional context for code generation.

        Returns:
            Result dictionary with generated code.
        """
        try:
            self.history.append({
                "action": "natural_language_to_code",
                "description": description,
                "language": language,
                "context": context,
                "timestamp": time.time()
            })
            
            # Analyze the description to understand intent
            intent_analysis = self.intent_recognition(description)
            
            # Generate code based on intent and language
            generated_code = self._generate_code_from_intent(
                description, language, intent_analysis, context
            )
            
            # Add explanatory comments
            commented_code = self._add_code_comments(generated_code, language, description)
            
            result = {
                "success": True,
                "description": description,
                "language": language,
                "intent_analysis": intent_analysis,
                "generated_code": commented_code,
                "code_length": len(commented_code),
                "estimated_complexity": self._estimate_code_complexity(commented_code, language)
            }
            
            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result
            
        except Exception as e:
            error_msg = f"Failed to convert natural language to code: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in natural language to code: {e}")
            return {
                "success": False,
                "error": error_msg,
                "description": description
            }

    def intent_recognition(self, text: str) -> Dict[str, Any]:
        """Recognize intent from natural language text.

        Args:
            text: Natural language text to analyze.

        Returns:
            Result dictionary with recognized intent.
        """
        try:
            self.history.append({
                "action": "intent_recognition",
                "text": text,
                "timestamp": time.time()
            })
            
            text_lower = text.lower()
            recognized_intents = []
            confidence_scores = {}
            
            # Check against intent patterns
            for intent, patterns in self.intent_patterns.items():
                for pattern in patterns:
                    matches = re.findall(pattern, text_lower)
                    if matches:
                        recognized_intents.append(intent)
                        confidence_scores[intent] = confidence_scores.get(intent, 0) + len(matches)
            
            # Determine primary intent
            primary_intent = max(confidence_scores.keys(), key=confidence_scores.get) if confidence_scores else "unknown"
            
            # Extract entities (nouns, objects)
            entities = self._extract_entities(text)
            
            # Determine action type
            action_type = self._determine_action_type(text_lower)
            
            result = {
                "success": True,
                "text": text,
                "primary_intent": primary_intent,
                "all_intents": recognized_intents,
                "confidence_scores": confidence_scores,
                "entities": entities,
                "action_type": action_type,
                "complexity": "high" if len(recognized_intents) > 2 else "medium" if recognized_intents else "low"
            }
            
            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result
            
        except Exception as e:
            error_msg = f"Failed to recognize intent: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in intent recognition: {e}")
            return {
                "success": False,
                "error": error_msg,
                "text": text
            }

    def _extract_entities(self, text: str) -> List[str]:
        """Extract entities (nouns, objects) from text."""
        # Simple entity extraction using common patterns
        entities = []
        
        # Extract words that might be entities (capitalized words, technical terms)
        words = text.split()
        for word in words:
            clean_word = re.sub(r'[^\w]', '', word)
            if (len(clean_word) > 2 and 
                (clean_word[0].isupper() or 
                 clean_word in ['function', 'class', 'method', 'variable', 'file', 'database', 'api', 'server'])):
                entities.append(clean_word.lower())
        
        return list(set(entities))  # Remove duplicates

    def _determine_action_type(self, text: str) -> str:
        """Determine the type of action from text."""
        if any(word in text for word in ['create', 'make', 'build', 'generate', 'new']):
            return "creation"
        elif any(word in text for word in ['modify', 'change', 'update', 'edit', 'fix']):
            return "modification"
        elif any(word in text for word in ['delete', 'remove', 'drop', 'clear']):
            return "deletion"
        elif any(word in text for word in ['analyze', 'check', 'examine', 'review', 'test']):
            return "analysis"
        elif any(word in text for word in ['run', 'execute', 'start', 'launch']):
            return "execution"
        else:
            return "unknown"

    def _generate_code_from_intent(self, description: str, language: str, 
                                 intent_analysis: Dict[str, Any], context: Optional[str]) -> str:
        """Generate code based on intent analysis."""
        primary_intent = intent_analysis.get("primary_intent", "unknown")
        entities = intent_analysis.get("entities", [])
        action_type = intent_analysis.get("action_type", "unknown")
        
        if language == "python":
            return self._generate_python_code(description, primary_intent, entities, action_type, context)
        elif language == "javascript":
            return self._generate_javascript_code(description, primary_intent, entities, action_type, context)
        else:
            return f"# Code generation for {language} not yet implemented\n# Description: {description}"

    def _generate_python_code(self, description: str, intent: str, entities: List[str], 
                            action_type: str, context: Optional[str]) -> str:
        """Generate Python code based on intent."""
        code_parts = []
        
        # Add imports if needed
        if any(entity in ['file', 'json', 'data'] for entity in entities):
            code_parts.append("import json")
            code_parts.append("import os")
        
        if intent == "create":
            if "function" in entities:
                func_name = next((e for e in entities if e not in ['function']), 'new_function')
                code_parts.append(f"\ndef {func_name}():")
                code_parts.append(f'    """Generated function for: {description}"""')
                code_parts.append("    # TODO: Implement function logic")
                code_parts.append("    pass")
            
            elif "class" in entities:
                class_name = next((e.capitalize() for e in entities if e not in ['class']), 'NewClass')
                code_parts.append(f"\nclass {class_name}:")
                code_parts.append(f'    """Generated class for: {description}"""')
                code_parts.append("    ")
                code_parts.append("    def __init__(self):")
                code_parts.append("        # TODO: Initialize class attributes")
                code_parts.append("        pass")
        
        elif intent == "analyze":
            code_parts.append("\ndef analyze_data(data):")
            code_parts.append(f'    """Analyze data as requested: {description}"""')
            code_parts.append("    # TODO: Implement analysis logic")
            code_parts.append("    results = {}")
            code_parts.append("    return results")
        
        # Default fallback
        if not code_parts:
            code_parts.append(f"# Generated code for: {description}")
            code_parts.append("# TODO: Implement the requested functionality")
        
        return "\n".join(code_parts)

    def _generate_javascript_code(self, description: str, intent: str, entities: List[str], 
                                action_type: str, context: Optional[str]) -> str:
        """Generate JavaScript code based on intent."""
        code_parts = []
        
        if intent == "create":
            if "function" in entities:
                func_name = next((e for e in entities if e not in ['function']), 'newFunction')
                code_parts.append(f"function {func_name}() {{")
                code_parts.append(f"    // Generated function for: {description}")
                code_parts.append("    // TODO: Implement function logic")
                code_parts.append("}")
            
            elif "class" in entities:
                class_name = next((e.capitalize() for e in entities if e not in ['class']), 'NewClass')
                code_parts.append(f"class {class_name} {{")
                code_parts.append(f"    // Generated class for: {description}")
                code_parts.append("    constructor() {")
                code_parts.append("        // TODO: Initialize class properties")
                code_parts.append("    }")
                code_parts.append("}")
        
        # Default fallback
        if not code_parts:
            code_parts.append(f"// Generated code for: {description}")
            code_parts.append("// TODO: Implement the requested functionality")
        
        return "\n".join(code_parts)

    def _add_code_comments(self, code: str, language: str, description: str) -> str:
        """Add explanatory comments to generated code."""
        if language == "python":
            header = f'"""\nGenerated code for: {description}\nAuto-generated by AI Reasoning Tool\n"""\n\n'
        elif language == "javascript":
            header = f'/**\n * Generated code for: {description}\n * Auto-generated by AI Reasoning Tool\n */\n\n'
        else:
            header = f"// Generated code for: {description}\n// Auto-generated by AI Reasoning Tool\n\n"
        
        return header + code

    def _estimate_code_complexity(self, code: str, language: str) -> str:
        """Estimate the complexity of generated code."""
        lines = len(code.split('\n'))
        functions = len(re.findall(r'def\s+\w+|function\s+\w+|class\s+\w+', code))
        
        if lines > 50 or functions > 5:
            return "high"
        elif lines > 20 or functions > 2:
            return "medium"
        else:
            return "low"
