"""
Advanced Testing and Debugging Tool for the AI Agent.
Implements comprehensive testing, debugging, and code analysis capabilities.
"""

import os
import re
import ast
import sys
import json
import subprocess
import traceback
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
import time
import tempfile

logger = logging.getLogger(__name__)

class TestingTool:
    """Advanced tool for testing, debugging, and code analysis."""

    def __init__(self, workspace_dir: Path):
        """Initialize the testing tool.

        Args:
            workspace_dir: The workspace directory to use.
        """
        self.workspace_dir = workspace_dir
        self.history: List[Dict[str, Any]] = []
        
        # Test frameworks and their patterns
        self.test_frameworks = {
            'python': {
                'pytest': {'patterns': ['test_*.py', '*_test.py'], 'command': 'pytest'},
                'unittest': {'patterns': ['test_*.py'], 'command': 'python -m unittest'},
                'nose': {'patterns': ['test_*.py'], 'command': 'nosetests'}
            },
            'javascript': {
                'jest': {'patterns': ['*.test.js', '*.spec.js'], 'command': 'npm test'},
                'mocha': {'patterns': ['*.test.js', '*.spec.js'], 'command': 'mocha'},
                'jasmine': {'patterns': ['*.spec.js'], 'command': 'jasmine'}
            },
            'typescript': {
                'jest': {'patterns': ['*.test.ts', '*.spec.ts'], 'command': 'npm test'},
                'mocha': {'patterns': ['*.test.ts', '*.spec.ts'], 'command': 'mocha'}
            }
        }
        
        # Common linters and their commands
        self.linters = {
            'python': {
                'pylint': 'pylint',
                'flake8': 'flake8',
                'black': 'black --check',
                'mypy': 'mypy'
            },
            'javascript': {
                'eslint': 'eslint',
                'jshint': 'jshint',
                'prettier': 'prettier --check'
            },
            'typescript': {
                'tslint': 'tslint',
                'eslint': 'eslint'
            }
        }

    def test_search(self, source_file: Optional[str] = None, test_type: str = "related") -> Dict[str, Any]:
        """Find tests related to source files.

        Args:
            source_file: The source file to find tests for.
            test_type: Type of search ('related', 'all', 'pattern').

        Returns:
            Result dictionary with found tests.
        """
        try:
            self.history.append({
                "action": "test_search",
                "source_file": source_file,
                "test_type": test_type,
                "timestamp": time.time()
            })
            
            found_tests = []
            
            if test_type == "all":
                # Find all test files
                for lang, frameworks in self.test_frameworks.items():
                    for framework, config in frameworks.items():
                        for pattern in config['patterns']:
                            test_files = list(self.workspace_dir.rglob(pattern))
                            for test_file in test_files:
                                relative_path = test_file.relative_to(self.workspace_dir)
                                found_tests.append({
                                    "test_file": str(relative_path),
                                    "framework": framework,
                                    "language": lang,
                                    "size": test_file.stat().st_size,
                                    "modified": test_file.stat().st_mtime
                                })
            
            elif test_type == "related" and source_file:
                # Find tests related to a specific source file
                source_path = Path(source_file)
                source_name = source_path.stem
                source_ext = source_path.suffix
                
                # Determine language from extension
                lang_map = {'.py': 'python', '.js': 'javascript', '.ts': 'typescript'}
                language = lang_map.get(source_ext, 'unknown')
                
                if language in self.test_frameworks:
                    for framework, config in self.test_frameworks[language].items():
                        # Look for test files that might test this source file
                        possible_test_names = [
                            f"test_{source_name}.py",
                            f"{source_name}_test.py",
                            f"test_{source_name}.js",
                            f"{source_name}.test.js",
                            f"{source_name}.spec.js",
                            f"test_{source_name}.ts",
                            f"{source_name}.test.ts",
                            f"{source_name}.spec.ts"
                        ]
                        
                        for test_name in possible_test_names:
                            test_files = list(self.workspace_dir.rglob(test_name))
                            for test_file in test_files:
                                relative_path = test_file.relative_to(self.workspace_dir)
                                found_tests.append({
                                    "test_file": str(relative_path),
                                    "source_file": source_file,
                                    "framework": framework,
                                    "language": language,
                                    "relationship": "name_based",
                                    "size": test_file.stat().st_size,
                                    "modified": test_file.stat().st_mtime
                                })
                        
                        # Also search for tests that import or reference the source file
                        for pattern in config['patterns']:
                            test_files = list(self.workspace_dir.rglob(pattern))
                            for test_file in test_files:
                                try:
                                    content = test_file.read_text(encoding='utf-8')
                                    if source_name in content or source_file in content:
                                        relative_path = test_file.relative_to(self.workspace_dir)
                                        found_tests.append({
                                            "test_file": str(relative_path),
                                            "source_file": source_file,
                                            "framework": framework,
                                            "language": language,
                                            "relationship": "content_reference",
                                            "size": test_file.stat().st_size,
                                            "modified": test_file.stat().st_mtime
                                        })
                                except Exception as e:
                                    logger.warning(f"Error reading test file {test_file}: {e}")
                                    continue
            
            # Remove duplicates
            unique_tests = []
            seen_files = set()
            for test in found_tests:
                if test["test_file"] not in seen_files:
                    unique_tests.append(test)
                    seen_files.add(test["test_file"])
            
            result = {
                "success": True,
                "source_file": source_file,
                "test_type": test_type,
                "total_tests_found": len(unique_tests),
                "tests": unique_tests
            }
            
            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result
            
        except Exception as e:
            error_msg = f"Failed to search tests: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in test search: {e}")
            return {
                "success": False,
                "error": error_msg,
                "source_file": source_file
            }

    def test_failure(self, test_file: Optional[str] = None, capture_output: bool = True) -> Dict[str, Any]:
        """Capture and analyze test failure messages.

        Args:
            test_file: Specific test file to analyze (if None, analyzes recent test runs).
            capture_output: Whether to capture detailed output.

        Returns:
            Result dictionary with failure analysis.
        """
        try:
            self.history.append({
                "action": "test_failure",
                "test_file": test_file,
                "timestamp": time.time()
            })
            
            failures = []
            
            if test_file:
                # Analyze specific test file
                test_path = self.workspace_dir / test_file
                if not test_path.exists():
                    return {
                        "success": False,
                        "error": f"Test file not found: {test_file}"
                    }
                
                # Determine test framework and run tests
                file_ext = test_path.suffix
                if file_ext == '.py':
                    # Try pytest first, then unittest
                    try:
                        result = subprocess.run(
                            ['pytest', str(test_path), '-v', '--tb=short'],
                            cwd=self.workspace_dir,
                            capture_output=True,
                            text=True,
                            timeout=60
                        )
                        
                        if result.returncode != 0:
                            failures.append({
                                "test_file": test_file,
                                "framework": "pytest",
                                "exit_code": result.returncode,
                                "stdout": result.stdout,
                                "stderr": result.stderr,
                                "analysis": self._analyze_python_test_failure(result.stdout + result.stderr)
                            })
                    except (subprocess.TimeoutExpired, FileNotFoundError):
                        # Fallback to unittest
                        try:
                            result = subprocess.run(
                                [sys.executable, '-m', 'unittest', test_file.replace('/', '.').replace('.py', '')],
                                cwd=self.workspace_dir,
                                capture_output=True,
                                text=True,
                                timeout=60
                            )
                            
                            if result.returncode != 0:
                                failures.append({
                                    "test_file": test_file,
                                    "framework": "unittest",
                                    "exit_code": result.returncode,
                                    "stdout": result.stdout,
                                    "stderr": result.stderr,
                                    "analysis": self._analyze_python_test_failure(result.stdout + result.stderr)
                                })
                        except Exception as e:
                            failures.append({
                                "test_file": test_file,
                                "error": f"Failed to run tests: {str(e)}"
                            })
                
                elif file_ext in ['.js', '.ts']:
                    # Try npm test or specific test runners
                    try:
                        result = subprocess.run(
                            ['npm', 'test', test_file],
                            cwd=self.workspace_dir,
                            capture_output=True,
                            text=True,
                            timeout=60
                        )
                        
                        if result.returncode != 0:
                            failures.append({
                                "test_file": test_file,
                                "framework": "npm",
                                "exit_code": result.returncode,
                                "stdout": result.stdout,
                                "stderr": result.stderr,
                                "analysis": self._analyze_js_test_failure(result.stdout + result.stderr)
                            })
                    except Exception as e:
                        failures.append({
                            "test_file": test_file,
                            "error": f"Failed to run JavaScript tests: {str(e)}"
                        })
            
            result = {
                "success": True,
                "test_file": test_file,
                "total_failures": len(failures),
                "failures": failures
            }
            
            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result
            
        except Exception as e:
            error_msg = f"Failed to analyze test failures: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in test failure analysis: {e}")
            return {
                "success": False,
                "error": error_msg,
                "test_file": test_file
            }

    def _analyze_python_test_failure(self, output: str) -> Dict[str, Any]:
        """Analyze Python test failure output."""
        analysis = {
            "error_type": "unknown",
            "error_message": "",
            "failed_tests": [],
            "suggestions": []
        }

        lines = output.split('\n')
        for i, line in enumerate(lines):
            if 'FAILED' in line:
                analysis["failed_tests"].append(line.strip())
            elif 'AssertionError' in line:
                analysis["error_type"] = "AssertionError"
                analysis["error_message"] = line.strip()
                analysis["suggestions"].append("Check assertion conditions and expected values")
            elif 'ImportError' in line or 'ModuleNotFoundError' in line:
                analysis["error_type"] = "ImportError"
                analysis["error_message"] = line.strip()
                analysis["suggestions"].append("Check import statements and module availability")
            elif 'SyntaxError' in line:
                analysis["error_type"] = "SyntaxError"
                analysis["error_message"] = line.strip()
                analysis["suggestions"].append("Fix syntax errors in the code")

        return analysis

    def _analyze_js_test_failure(self, output: str) -> Dict[str, Any]:
        """Analyze JavaScript test failure output."""
        analysis = {
            "error_type": "unknown",
            "error_message": "",
            "failed_tests": [],
            "suggestions": []
        }

        lines = output.split('\n')
        for line in lines:
            if 'FAIL' in line or 'Failed' in line:
                analysis["failed_tests"].append(line.strip())
            elif 'ReferenceError' in line:
                analysis["error_type"] = "ReferenceError"
                analysis["error_message"] = line.strip()
                analysis["suggestions"].append("Check variable and function references")
            elif 'TypeError' in line:
                analysis["error_type"] = "TypeError"
                analysis["error_message"] = line.strip()
                analysis["suggestions"].append("Check data types and method calls")

        return analysis

    def run_tests(self, test_pattern: Optional[str] = None, framework: Optional[str] = None,
                  verbose: bool = True) -> Dict[str, Any]:
        """Automatically execute test suites.

        Args:
            test_pattern: Pattern to match test files (e.g., "test_*.py").
            framework: Specific test framework to use.
            verbose: Whether to include verbose output.

        Returns:
            Result dictionary with test execution results.
        """
        try:
            self.history.append({
                "action": "run_tests",
                "test_pattern": test_pattern,
                "framework": framework,
                "timestamp": time.time()
            })

            test_results = []

            # Detect project type and available test frameworks
            if not framework:
                # Auto-detect framework
                if (self.workspace_dir / "pytest.ini").exists() or (self.workspace_dir / "pyproject.toml").exists():
                    framework = "pytest"
                elif (self.workspace_dir / "package.json").exists():
                    framework = "npm"
                else:
                    framework = "auto"

            if framework == "pytest" or framework == "auto":
                # Run Python tests with pytest
                try:
                    cmd = ["pytest"]
                    if test_pattern:
                        cmd.extend(["-k", test_pattern])
                    if verbose:
                        cmd.append("-v")
                    cmd.extend(["--tb=short", "--json-report", "--json-report-file=/tmp/pytest_report.json"])

                    result = subprocess.run(
                        cmd,
                        cwd=self.workspace_dir,
                        capture_output=True,
                        text=True,
                        timeout=300  # 5 minutes timeout
                    )

                    test_results.append({
                        "framework": "pytest",
                        "exit_code": result.returncode,
                        "stdout": result.stdout,
                        "stderr": result.stderr,
                        "success": result.returncode == 0,
                        "duration": "unknown"
                    })

                except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                    test_results.append({
                        "framework": "pytest",
                        "error": str(e),
                        "success": False
                    })

            if framework == "npm" or framework == "auto":
                # Run JavaScript/TypeScript tests with npm
                try:
                    result = subprocess.run(
                        ["npm", "test"],
                        cwd=self.workspace_dir,
                        capture_output=True,
                        text=True,
                        timeout=300
                    )

                    test_results.append({
                        "framework": "npm",
                        "exit_code": result.returncode,
                        "stdout": result.stdout,
                        "stderr": result.stderr,
                        "success": result.returncode == 0,
                        "duration": "unknown"
                    })

                except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                    test_results.append({
                        "framework": "npm",
                        "error": str(e),
                        "success": False
                    })

            # Calculate overall success
            overall_success = all(result.get("success", False) for result in test_results)

            result = {
                "success": True,
                "test_pattern": test_pattern,
                "framework": framework,
                "overall_test_success": overall_success,
                "total_frameworks_run": len(test_results),
                "results": test_results
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to run tests: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error running tests: {e}")
            return {
                "success": False,
                "error": error_msg,
                "test_pattern": test_pattern
            }

    def lint_check(self, file_path: Optional[str] = None, linter: Optional[str] = None,
                   fix_issues: bool = False) -> Dict[str, Any]:
        """Run lint/static analysis on code.

        Args:
            file_path: Specific file to lint (if None, lints all relevant files).
            linter: Specific linter to use.
            fix_issues: Whether to attempt to fix issues automatically.

        Returns:
            Result dictionary with lint results.
        """
        try:
            self.history.append({
                "action": "lint_check",
                "file_path": file_path,
                "linter": linter,
                "fix_issues": fix_issues,
                "timestamp": time.time()
            })

            lint_results = []

            # Determine files to lint
            files_to_lint = []
            if file_path:
                files_to_lint = [self.workspace_dir / file_path]
            else:
                # Find all code files
                for ext in ['.py', '.js', '.ts', '.jsx', '.tsx']:
                    files_to_lint.extend(self.workspace_dir.rglob(f"*{ext}"))

            # Group files by language
            files_by_lang = {}
            for file in files_to_lint:
                if file.exists() and file.is_file():
                    ext = file.suffix
                    if ext == '.py':
                        lang = 'python'
                    elif ext in ['.js', '.jsx']:
                        lang = 'javascript'
                    elif ext in ['.ts', '.tsx']:
                        lang = 'typescript'
                    else:
                        continue

                    if lang not in files_by_lang:
                        files_by_lang[lang] = []
                    files_by_lang[lang].append(file)

            # Run linters for each language
            for lang, files in files_by_lang.items():
                if lang in self.linters:
                    for linter_name, linter_cmd in self.linters[lang].items():
                        if linter and linter != linter_name:
                            continue

                        try:
                            # Prepare command
                            cmd_parts = linter_cmd.split()
                            if fix_issues and linter_name in ['black', 'prettier']:
                                # Remove --check flag for fixing
                                cmd_parts = [part for part in cmd_parts if part != '--check']

                            # Add files to command
                            cmd_parts.extend([str(f) for f in files])

                            result = subprocess.run(
                                cmd_parts,
                                cwd=self.workspace_dir,
                                capture_output=True,
                                text=True,
                                timeout=120
                            )

                            lint_results.append({
                                "linter": linter_name,
                                "language": lang,
                                "files_checked": len(files),
                                "exit_code": result.returncode,
                                "stdout": result.stdout,
                                "stderr": result.stderr,
                                "success": result.returncode == 0,
                                "issues_found": result.returncode != 0,
                                "fixed_issues": fix_issues and result.returncode == 0
                            })

                        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                            lint_results.append({
                                "linter": linter_name,
                                "language": lang,
                                "error": str(e),
                                "success": False
                            })

            result = {
                "success": True,
                "file_path": file_path,
                "linter": linter,
                "fix_issues": fix_issues,
                "total_linters_run": len(lint_results),
                "results": lint_results
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to run lint check: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in lint check: {e}")
            return {
                "success": False,
                "error": error_msg,
                "file_path": file_path
            }

    def autonomous_debugger(self, file_path: str, error_description: Optional[str] = None) -> Dict[str, Any]:
        """Trace, identify and fix bugs automatically.

        Args:
            file_path: The file to debug.
            error_description: Description of the error or issue.

        Returns:
            Result dictionary with debugging analysis and suggestions.
        """
        try:
            self.history.append({
                "action": "autonomous_debugger",
                "file_path": file_path,
                "error_description": error_description,
                "timestamp": time.time()
            })

            debug_analysis = {
                "file_path": file_path,
                "issues_found": [],
                "suggestions": [],
                "auto_fixes": [],
                "severity_levels": {}
            }

            file_full_path = self.workspace_dir / file_path
            if not file_full_path.exists():
                return {
                    "success": False,
                    "error": f"File not found: {file_path}"
                }

            # Read file content
            try:
                content = file_full_path.read_text(encoding='utf-8')
            except Exception as e:
                return {
                    "success": False,
                    "error": f"Cannot read file: {str(e)}"
                }

            # Analyze based on file type
            file_ext = file_full_path.suffix

            if file_ext == '.py':
                debug_analysis.update(self._debug_python_file(content, file_path))
            elif file_ext in ['.js', '.ts', '.jsx', '.tsx']:
                debug_analysis.update(self._debug_javascript_file(content, file_path))
            else:
                debug_analysis["suggestions"].append(f"Unsupported file type for debugging: {file_ext}")

            # Add error-specific analysis if provided
            if error_description:
                debug_analysis["error_analysis"] = self._analyze_error_description(error_description, content)

            result = {
                "success": True,
                "file_path": file_path,
                "error_description": error_description,
                "debug_analysis": debug_analysis
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to debug file: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in autonomous debugger: {e}")
            return {
                "success": False,
                "error": error_msg,
                "file_path": file_path
            }

    def _debug_python_file(self, content: str, file_path: str) -> Dict[str, Any]:
        """Debug Python file content."""
        issues = []
        suggestions = []
        auto_fixes = []

        lines = content.split('\n')

        # Check for syntax errors
        try:
            ast.parse(content)
        except SyntaxError as e:
            issues.append({
                "type": "syntax_error",
                "line": e.lineno,
                "message": e.msg,
                "severity": "high"
            })
            suggestions.append(f"Fix syntax error at line {e.lineno}: {e.msg}")

        # Check for common issues
        for line_num, line in enumerate(lines, 1):
            line_stripped = line.strip()

            # Check for unused imports (basic check)
            if line_stripped.startswith('import ') or line_stripped.startswith('from '):
                import_name = line_stripped.split()[-1] if 'import' in line_stripped else line_stripped.split()[1]
                if import_name not in content[content.find(line):]:
                    issues.append({
                        "type": "unused_import",
                        "line": line_num,
                        "message": f"Potentially unused import: {import_name}",
                        "severity": "low"
                    })
                    auto_fixes.append({
                        "type": "remove_line",
                        "line": line_num,
                        "description": f"Remove unused import: {line_stripped}"
                    })

            # Check for print statements (potential debugging leftovers)
            if 'print(' in line_stripped and not line_stripped.startswith('#'):
                issues.append({
                    "type": "debug_print",
                    "line": line_num,
                    "message": "Print statement found (potential debug leftover)",
                    "severity": "low"
                })

            # Check for TODO/FIXME comments
            if any(keyword in line_stripped.upper() for keyword in ['TODO', 'FIXME', 'HACK', 'XXX']):
                issues.append({
                    "type": "todo_comment",
                    "line": line_num,
                    "message": "TODO/FIXME comment found",
                    "severity": "medium"
                })

            # Check for long lines
            if len(line) > 120:
                issues.append({
                    "type": "long_line",
                    "line": line_num,
                    "message": f"Line too long ({len(line)} characters)",
                    "severity": "low"
                })

        return {
            "issues_found": issues,
            "suggestions": suggestions,
            "auto_fixes": auto_fixes
        }

    def _debug_javascript_file(self, content: str, file_path: str) -> Dict[str, Any]:
        """Debug JavaScript/TypeScript file content."""
        issues = []
        suggestions = []
        auto_fixes = []

        lines = content.split('\n')

        # Check for common JavaScript issues
        for line_num, line in enumerate(lines, 1):
            line_stripped = line.strip()

            # Check for console.log statements
            if 'console.log(' in line_stripped and not line_stripped.startswith('//'):
                issues.append({
                    "type": "debug_console",
                    "line": line_num,
                    "message": "Console.log statement found (potential debug leftover)",
                    "severity": "low"
                })

            # Check for var usage (prefer let/const)
            if line_stripped.startswith('var '):
                issues.append({
                    "type": "var_usage",
                    "line": line_num,
                    "message": "Use 'let' or 'const' instead of 'var'",
                    "severity": "medium"
                })
                auto_fixes.append({
                    "type": "replace_var",
                    "line": line_num,
                    "description": "Replace 'var' with 'let' or 'const'"
                })

            # Check for == instead of ===
            if ' == ' in line_stripped and ' === ' not in line_stripped:
                issues.append({
                    "type": "loose_equality",
                    "line": line_num,
                    "message": "Use strict equality (===) instead of loose equality (==)",
                    "severity": "medium"
                })

        return {
            "issues_found": issues,
            "suggestions": suggestions,
            "auto_fixes": auto_fixes
        }

    def _analyze_error_description(self, error_description: str, content: str) -> Dict[str, Any]:
        """Analyze error description and provide specific suggestions."""
        analysis = {
            "error_type": "unknown",
            "likely_causes": [],
            "specific_suggestions": []
        }

        error_lower = error_description.lower()

        if "importerror" in error_lower or "modulenotfounderror" in error_lower:
            analysis["error_type"] = "import_error"
            analysis["likely_causes"] = [
                "Missing dependency",
                "Incorrect import path",
                "Module not installed"
            ]
            analysis["specific_suggestions"] = [
                "Check if the module is installed",
                "Verify import paths",
                "Install missing dependencies"
            ]

        elif "syntaxerror" in error_lower:
            analysis["error_type"] = "syntax_error"
            analysis["likely_causes"] = [
                "Missing parentheses or brackets",
                "Incorrect indentation",
                "Invalid syntax"
            ]
            analysis["specific_suggestions"] = [
                "Check for matching parentheses and brackets",
                "Verify indentation consistency",
                "Review syntax for the specific language"
            ]

        elif "nameerror" in error_lower:
            analysis["error_type"] = "name_error"
            analysis["likely_causes"] = [
                "Undefined variable",
                "Typo in variable name",
                "Variable used before definition"
            ]
            analysis["specific_suggestions"] = [
                "Check variable names for typos",
                "Ensure variables are defined before use",
                "Check variable scope"
            ]

        return analysis

    def self_repair(self, file_path: str, issues: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """AI-suggested code fixes based on errors.

        Args:
            file_path: The file to repair.
            issues: List of issues to fix (if None, will detect issues first).

        Returns:
            Result dictionary with repair results.
        """
        try:
            self.history.append({
                "action": "self_repair",
                "file_path": file_path,
                "timestamp": time.time()
            })

            # If no issues provided, detect them first
            if issues is None:
                debug_result = self.autonomous_debugger(file_path)
                if not debug_result["success"]:
                    return debug_result
                issues = debug_result["debug_analysis"]["issues_found"]

            file_full_path = self.workspace_dir / file_path
            content = file_full_path.read_text(encoding='utf-8')
            lines = content.split('\n')

            repairs_made = []
            modified_lines = {}

            # Apply auto-fixes
            for issue in issues:
                if issue.get("severity") == "high" or issue.get("type") in ["unused_import", "var_usage"]:
                    line_num = issue.get("line", 0)
                    if line_num > 0 and line_num <= len(lines):
                        original_line = lines[line_num - 1]

                        if issue["type"] == "unused_import":
                            # Comment out unused import
                            lines[line_num - 1] = f"# {original_line}"
                            repairs_made.append({
                                "type": "commented_unused_import",
                                "line": line_num,
                                "original": original_line,
                                "fixed": lines[line_num - 1]
                            })

                        elif issue["type"] == "var_usage" and file_path.endswith('.js'):
                            # Replace var with let
                            lines[line_num - 1] = original_line.replace('var ', 'let ', 1)
                            repairs_made.append({
                                "type": "replaced_var_with_let",
                                "line": line_num,
                                "original": original_line,
                                "fixed": lines[line_num - 1]
                            })

            # Write repaired content back to file
            if repairs_made:
                repaired_content = '\n'.join(lines)

                # Create backup
                backup_path = file_full_path.with_suffix(file_full_path.suffix + '.backup')
                backup_path.write_text(content, encoding='utf-8')

                # Write repaired content
                file_full_path.write_text(repaired_content, encoding='utf-8')

            result = {
                "success": True,
                "file_path": file_path,
                "total_issues": len(issues),
                "repairs_made": len(repairs_made),
                "repairs": repairs_made,
                "backup_created": len(repairs_made) > 0
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to repair file: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in self repair: {e}")
            return {
                "success": False,
                "error": error_msg,
                "file_path": file_path
            }

    def code_linting_static_analysis(self, target: Optional[str] = None) -> Dict[str, Any]:
        """Combine all error & code checkers.

        Args:
            target: Specific file or directory to analyze.

        Returns:
            Result dictionary with comprehensive analysis.
        """
        try:
            self.history.append({
                "action": "code_linting_static_analysis",
                "target": target,
                "timestamp": time.time()
            })

            analysis_results = {
                "lint_results": [],
                "debug_results": [],
                "test_results": [],
                "overall_score": 0,
                "recommendations": []
            }

            # Run lint checks
            lint_result = self.lint_check(target)
            analysis_results["lint_results"] = lint_result.get("results", [])

            # Run debugging analysis on code files
            if target and Path(target).suffix in ['.py', '.js', '.ts', '.jsx', '.tsx']:
                debug_result = self.autonomous_debugger(target)
                if debug_result["success"]:
                    analysis_results["debug_results"] = [debug_result["debug_analysis"]]
            else:
                # Analyze multiple files
                for ext in ['.py', '.js', '.ts']:
                    for file_path in self.workspace_dir.rglob(f"*{ext}"):
                        if file_path.is_file():
                            relative_path = str(file_path.relative_to(self.workspace_dir))
                            debug_result = self.autonomous_debugger(relative_path)
                            if debug_result["success"]:
                                analysis_results["debug_results"].append(debug_result["debug_analysis"])

                            # Limit to first 10 files to avoid overwhelming output
                            if len(analysis_results["debug_results"]) >= 10:
                                break

            # Run tests if available
            test_result = self.run_tests()
            analysis_results["test_results"] = test_result.get("results", [])

            # Calculate overall score
            total_issues = sum(len(debug.get("issues_found", [])) for debug in analysis_results["debug_results"])
            total_files = len(analysis_results["debug_results"])

            if total_files > 0:
                analysis_results["overall_score"] = max(0, 100 - (total_issues * 10 / total_files))
            else:
                analysis_results["overall_score"] = 100

            # Generate recommendations
            if total_issues > 0:
                analysis_results["recommendations"].extend([
                    "Address high-severity issues first",
                    "Run automated fixes where possible",
                    "Add or improve test coverage"
                ])

            result = {
                "success": True,
                "target": target,
                "analysis": analysis_results
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to perform comprehensive analysis: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in comprehensive analysis: {e}")
            return {
                "success": False,
                "error": error_msg,
                "target": target
            }

    def get_history(self) -> List[Dict[str, Any]]:
        """Get the testing tool operation history."""
        return self.history
