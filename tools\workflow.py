"""
Advanced Workflow and Smart Agent Tool for the AI Agent.
Implements comprehensive workflow management, planning, and smart agent capabilities.
"""

import os
import re
import ast
import json
import time
import shutil
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Set
import tempfile
import hashlib

logger = logging.getLogger(__name__)

class WorkflowTool:
    """Advanced tool for workflow management and smart agent capabilities."""

    def __init__(self, workspace_dir: Path):
        """Initialize the workflow tool.

        Args:
            workspace_dir: The workspace directory to use.
        """
        self.workspace_dir = workspace_dir
        self.history: List[Dict[str, Any]] = []
        
        # Planning and memory storage
        self.planning_memory = {}
        self.context_memory = {}
        self.task_memory = {}
        
        # Code analysis cache
        self.code_analysis_cache = {}
        
        # Supported languages for translation and optimization
        self.supported_languages = {
            'python': {'.py'},
            'javascript': {'.js', '.jsx'},
            'typescript': {'.ts', '.tsx'},
            'java': {'.java'},
            'cpp': {'.cpp', '.cc', '.cxx'},
            'c': {'.c'},
            'csharp': {'.cs'},
            'go': {'.go'},
            'rust': {'.rs'},
            'php': {'.php'},
            'ruby': {'.rb'}
        }

    def create_new_workspace(self, workspace_name: str, template: Optional[str] = None,
                           initialize_git: bool = True) -> Dict[str, Any]:
        """Create and setup new project workspace.

        Args:
            workspace_name: Name of the new workspace.
            template: Template to use ('python', 'javascript', 'react', 'django', etc.).
            initialize_git: Whether to initialize git repository.

        Returns:
            Result dictionary with workspace creation details.
        """
        try:
            self.history.append({
                "action": "create_new_workspace",
                "workspace_name": workspace_name,
                "template": template,
                "initialize_git": initialize_git,
                "timestamp": time.time()
            })
            
            # Create workspace directory
            workspace_path = self.workspace_dir / workspace_name
            if workspace_path.exists():
                return {
                    "success": False,
                    "error": f"Workspace '{workspace_name}' already exists",
                    "workspace_path": str(workspace_path)
                }
            
            workspace_path.mkdir(parents=True, exist_ok=True)
            
            created_files = []
            
            # Apply template if specified
            if template:
                template_files = self._create_template_files(workspace_path, template)
                created_files.extend(template_files)
            
            # Initialize git repository
            if initialize_git:
                try:
                    subprocess.run(['git', 'init'], cwd=workspace_path, check=True, 
                                 capture_output=True, text=True)
                    
                    # Create .gitignore
                    gitignore_content = self._get_gitignore_template(template)
                    gitignore_path = workspace_path / '.gitignore'
                    gitignore_path.write_text(gitignore_content, encoding='utf-8')
                    created_files.append('.gitignore')
                    
                    # Initial commit
                    subprocess.run(['git', 'add', '.'], cwd=workspace_path, check=True)
                    subprocess.run(['git', 'commit', '-m', 'Initial commit'], 
                                 cwd=workspace_path, check=True)
                    
                except subprocess.CalledProcessError as e:
                    logger.warning(f"Git initialization failed: {e}")
            
            result = {
                "success": True,
                "workspace_name": workspace_name,
                "workspace_path": str(workspace_path),
                "template": template,
                "initialize_git": initialize_git,
                "created_files": created_files,
                "total_files": len(created_files)
            }
            
            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result
            
        except Exception as e:
            error_msg = f"Failed to create workspace: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error creating workspace: {e}")
            return {
                "success": False,
                "error": error_msg,
                "workspace_name": workspace_name
            }

    def _create_template_files(self, workspace_path: Path, template: str) -> List[str]:
        """Create template files based on project type."""
        created_files = []
        
        if template == "python":
            # Python project template
            files = {
                "main.py": "#!/usr/bin/env python3\n\ndef main():\n    print('Hello, World!')\n\nif __name__ == '__main__':\n    main()\n",
                "requirements.txt": "# Add your dependencies here\n",
                "README.md": f"# {workspace_path.name}\n\nA Python project.\n\n## Installation\n\n```bash\npip install -r requirements.txt\n```\n\n## Usage\n\n```bash\npython main.py\n```\n",
                "setup.py": f"from setuptools import setup, find_packages\n\nsetup(\n    name='{workspace_path.name}',\n    version='0.1.0',\n    packages=find_packages(),\n    install_requires=[],\n)\n"
            }
            
        elif template == "javascript":
            # JavaScript/Node.js project template
            files = {
                "package.json": json.dumps({
                    "name": workspace_path.name,
                    "version": "1.0.0",
                    "description": "",
                    "main": "index.js",
                    "scripts": {
                        "start": "node index.js",
                        "test": "echo \"Error: no test specified\" && exit 1"
                    },
                    "keywords": [],
                    "author": "",
                    "license": "ISC"
                }, indent=2),
                "index.js": "console.log('Hello, World!');\n",
                "README.md": f"# {workspace_path.name}\n\nA JavaScript project.\n\n## Installation\n\n```bash\nnpm install\n```\n\n## Usage\n\n```bash\nnpm start\n```\n"
            }
            
        elif template == "react":
            # React project template
            files = {
                "package.json": json.dumps({
                    "name": workspace_path.name,
                    "version": "0.1.0",
                    "private": True,
                    "dependencies": {
                        "react": "^18.2.0",
                        "react-dom": "^18.2.0",
                        "react-scripts": "5.0.1"
                    },
                    "scripts": {
                        "start": "react-scripts start",
                        "build": "react-scripts build",
                        "test": "react-scripts test",
                        "eject": "react-scripts eject"
                    }
                }, indent=2),
                "public/index.html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"utf-8\" />\n    <title>React App</title>\n</head>\n<body>\n    <div id=\"root\"></div>\n</body>\n</html>\n",
                "src/App.js": "import React from 'react';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <h1>Hello, React!</h1>\n    </div>\n  );\n}\n\nexport default App;\n",
                "src/index.js": "import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(<App />);\n"
            }
            
        else:
            # Generic template
            files = {
                "README.md": f"# {workspace_path.name}\n\nA new project.\n"
            }
        
        # Create the files
        for file_path, content in files.items():
            full_path = workspace_path / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(content, encoding='utf-8')
            created_files.append(file_path)
        
        return created_files

    def _get_gitignore_template(self, template: Optional[str]) -> str:
        """Get .gitignore template based on project type."""
        base_ignore = "# IDE and editor files\n.vscode/\n.idea/\n*.swp\n*.swo\n*~\n\n# OS files\n.DS_Store\nThumbs.db\n\n"
        
        if template == "python":
            return base_ignore + """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# Testing
.pytest_cache/
.coverage
htmlcov/

# Jupyter Notebook
.ipynb_checkpoints
"""
        
        elif template in ["javascript", "react"]:
            return base_ignore + """# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
"""
        
        else:
            return base_ignore

    def run_vscode_command(self, command: str, args: Optional[List[str]] = None) -> Dict[str, Any]:
        """Execute VSCode commands and extensions.

        Args:
            command: VSCode command to execute.
            args: Arguments for the command.

        Returns:
            Result dictionary with command execution details.
        """
        try:
            self.history.append({
                "action": "run_vscode_command",
                "command": command,
                "args": args,
                "timestamp": time.time()
            })
            
            # Build the VSCode command
            vscode_cmd = ["code"]
            
            if command == "open":
                # Open workspace or file
                if args:
                    vscode_cmd.extend(args)
                else:
                    vscode_cmd.append(str(self.workspace_dir))
            
            elif command == "install_extension":
                # Install VSCode extension
                if not args:
                    return {
                        "success": False,
                        "error": "No extension specified for installation"
                    }
                vscode_cmd.extend(["--install-extension", args[0]])
            
            elif command == "list_extensions":
                # List installed extensions
                vscode_cmd.append("--list-extensions")
            
            elif command == "new_file":
                # Create and open new file
                if args:
                    file_path = self.workspace_dir / args[0]
                    file_path.parent.mkdir(parents=True, exist_ok=True)
                    file_path.touch()
                    vscode_cmd.append(str(file_path))
            
            else:
                return {
                    "success": False,
                    "error": f"Unknown VSCode command: {command}"
                }
            
            # Execute the command
            result = subprocess.run(vscode_cmd, capture_output=True, text=True, timeout=30)
            
            command_result = {
                "success": result.returncode == 0,
                "command": command,
                "args": args,
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            self.history[-1]["success"] = command_result["success"]
            self.history[-1]["result"] = command_result
            return command_result
            
        except subprocess.TimeoutExpired:
            error_msg = "VSCode command timed out"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            return {
                "success": False,
                "error": error_msg,
                "command": command
            }
        except Exception as e:
            error_msg = f"Failed to execute VSCode command: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error executing VSCode command: {e}")
            return {
                "success": False,
                "error": error_msg,
                "command": command
            }

    def plan_next_step(self, current_context: str, goal: str,
                      previous_steps: Optional[List[str]] = None) -> Dict[str, Any]:
        """AI-powered planning for next development steps.

        Args:
            current_context: Current state/context of the project.
            goal: The desired goal or outcome.
            previous_steps: List of previously completed steps.

        Returns:
            Result dictionary with planning recommendations.
        """
        try:
            self.history.append({
                "action": "plan_next_step",
                "current_context": current_context,
                "goal": goal,
                "previous_steps": previous_steps,
                "timestamp": time.time()
            })

            # Store in planning memory
            plan_id = hashlib.md5(f"{current_context}{goal}{time.time()}".encode()).hexdigest()[:8]

            # Analyze current context
            context_analysis = self._analyze_context(current_context)

            # Generate next steps based on goal and context
            next_steps = self._generate_next_steps(current_context, goal, previous_steps, context_analysis)

            # Prioritize steps
            prioritized_steps = self._prioritize_steps(next_steps, goal)

            # Store planning session
            self.planning_memory[plan_id] = {
                "current_context": current_context,
                "goal": goal,
                "previous_steps": previous_steps or [],
                "context_analysis": context_analysis,
                "next_steps": prioritized_steps,
                "created_at": time.time()
            }

            result = {
                "success": True,
                "plan_id": plan_id,
                "current_context": current_context,
                "goal": goal,
                "context_analysis": context_analysis,
                "next_steps": prioritized_steps,
                "total_steps": len(prioritized_steps)
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to plan next step: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in planning: {e}")
            return {
                "success": False,
                "error": error_msg,
                "goal": goal
            }

    def _analyze_context(self, context: str) -> Dict[str, Any]:
        """Analyze the current context to understand the situation."""
        analysis = {
            "context_type": "unknown",
            "technologies": [],
            "challenges": [],
            "opportunities": [],
            "complexity": "medium"
        }

        context_lower = context.lower()

        # Detect technologies
        tech_keywords = {
            "python": ["python", "django", "flask", "fastapi", "pandas", "numpy"],
            "javascript": ["javascript", "js", "node", "react", "vue", "angular"],
            "web": ["html", "css", "web", "frontend", "backend", "api"],
            "database": ["database", "sql", "mongodb", "postgresql", "mysql"],
            "ai/ml": ["ai", "ml", "machine learning", "neural", "tensorflow", "pytorch"]
        }

        for tech, keywords in tech_keywords.items():
            if any(keyword in context_lower for keyword in keywords):
                analysis["technologies"].append(tech)

        # Detect context type
        if any(word in context_lower for word in ["bug", "error", "fix", "debug"]):
            analysis["context_type"] = "debugging"
        elif any(word in context_lower for word in ["new", "create", "start", "begin"]):
            analysis["context_type"] = "development"
        elif any(word in context_lower for word in ["improve", "optimize", "refactor"]):
            analysis["context_type"] = "optimization"
        elif any(word in context_lower for word in ["test", "testing", "coverage"]):
            analysis["context_type"] = "testing"

        # Assess complexity
        complexity_indicators = len(analysis["technologies"]) + context.count(",") + context.count("and")
        if complexity_indicators > 5:
            analysis["complexity"] = "high"
        elif complexity_indicators < 2:
            analysis["complexity"] = "low"

        return analysis

    def _generate_next_steps(self, context: str, goal: str, previous_steps: Optional[List[str]],
                           analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate next steps based on context and goal."""
        steps = []

        context_type = analysis.get("context_type", "unknown")
        technologies = analysis.get("technologies", [])

        if context_type == "development":
            if "python" in technologies:
                steps.extend([
                    {"step": "Set up Python virtual environment", "priority": "high", "estimated_time": "5 minutes"},
                    {"step": "Create project structure", "priority": "high", "estimated_time": "10 minutes"},
                    {"step": "Install required dependencies", "priority": "medium", "estimated_time": "5 minutes"},
                    {"step": "Write initial code structure", "priority": "medium", "estimated_time": "30 minutes"}
                ])

            if "javascript" in technologies:
                steps.extend([
                    {"step": "Initialize npm project", "priority": "high", "estimated_time": "5 minutes"},
                    {"step": "Set up build configuration", "priority": "medium", "estimated_time": "15 minutes"},
                    {"step": "Create component structure", "priority": "medium", "estimated_time": "20 minutes"}
                ])

        elif context_type == "debugging":
            steps.extend([
                {"step": "Reproduce the issue", "priority": "high", "estimated_time": "15 minutes"},
                {"step": "Add logging/debugging statements", "priority": "high", "estimated_time": "10 minutes"},
                {"step": "Analyze error messages and stack traces", "priority": "high", "estimated_time": "20 minutes"},
                {"step": "Implement fix", "priority": "medium", "estimated_time": "30 minutes"},
                {"step": "Test the fix", "priority": "high", "estimated_time": "15 minutes"}
            ])

        elif context_type == "optimization":
            steps.extend([
                {"step": "Profile current performance", "priority": "high", "estimated_time": "20 minutes"},
                {"step": "Identify bottlenecks", "priority": "high", "estimated_time": "15 minutes"},
                {"step": "Implement optimizations", "priority": "medium", "estimated_time": "45 minutes"},
                {"step": "Benchmark improvements", "priority": "medium", "estimated_time": "15 minutes"}
            ])

        elif context_type == "testing":
            steps.extend([
                {"step": "Set up testing framework", "priority": "high", "estimated_time": "10 minutes"},
                {"step": "Write unit tests", "priority": "high", "estimated_time": "60 minutes"},
                {"step": "Write integration tests", "priority": "medium", "estimated_time": "45 minutes"},
                {"step": "Set up continuous integration", "priority": "low", "estimated_time": "30 minutes"}
            ])

        # Add generic steps if no specific context detected
        if not steps:
            steps.extend([
                {"step": "Analyze current codebase", "priority": "high", "estimated_time": "20 minutes"},
                {"step": "Define clear requirements", "priority": "high", "estimated_time": "15 minutes"},
                {"step": "Create implementation plan", "priority": "medium", "estimated_time": "10 minutes"},
                {"step": "Start implementation", "priority": "medium", "estimated_time": "60 minutes"}
            ])

        # Filter out steps that might have been completed
        if previous_steps:
            previous_lower = [step.lower() for step in previous_steps]
            steps = [step for step in steps if not any(prev in step["step"].lower() for prev in previous_lower)]

        return steps

    def _prioritize_steps(self, steps: List[Dict[str, Any]], goal: str) -> List[Dict[str, Any]]:
        """Prioritize steps based on goal and dependencies."""
        # Sort by priority (high -> medium -> low) and then by estimated time
        priority_order = {"high": 3, "medium": 2, "low": 1}

        def priority_key(step):
            priority_score = priority_order.get(step.get("priority", "medium"), 2)
            # Convert time to minutes for sorting
            time_str = step.get("estimated_time", "30 minutes")
            time_minutes = int(re.search(r'\d+', time_str).group()) if re.search(r'\d+', time_str) else 30
            return (priority_score, -time_minutes)  # Negative for ascending time order within same priority

        sorted_steps = sorted(steps, key=priority_key, reverse=True)

        # Add step numbers
        for i, step in enumerate(sorted_steps, 1):
            step["step_number"] = i

        return sorted_steps

    def multi_step_loop(self, plan_id: str, execute_steps: bool = False) -> Dict[str, Any]:
        """Execute multi-step workflows with progress tracking.

        Args:
            plan_id: ID of the plan to execute.
            execute_steps: Whether to actually execute the steps or just simulate.

        Returns:
            Result dictionary with execution progress.
        """
        try:
            self.history.append({
                "action": "multi_step_loop",
                "plan_id": plan_id,
                "execute_steps": execute_steps,
                "timestamp": time.time()
            })

            # Get the plan
            if plan_id not in self.planning_memory:
                return {
                    "success": False,
                    "error": f"Plan ID '{plan_id}' not found"
                }

            plan = self.planning_memory[plan_id]
            steps = plan["next_steps"]

            execution_results = []
            completed_steps = 0
            failed_steps = 0

            for step in steps:
                step_result = {
                    "step_number": step["step_number"],
                    "step_description": step["step"],
                    "priority": step["priority"],
                    "estimated_time": step["estimated_time"],
                    "started_at": time.time()
                }

                if execute_steps:
                    # Simulate step execution (in real implementation, this would execute actual tasks)
                    try:
                        # Simulate execution time
                        import random
                        execution_time = random.uniform(0.5, 2.0)  # Simulate 0.5-2 seconds
                        time.sleep(execution_time)

                        step_result.update({
                            "status": "completed",
                            "execution_time": execution_time,
                            "success": True
                        })
                        completed_steps += 1

                    except Exception as e:
                        step_result.update({
                            "status": "failed",
                            "error": str(e),
                            "success": False
                        })
                        failed_steps += 1
                else:
                    step_result.update({
                        "status": "simulated",
                        "success": True
                    })
                    completed_steps += 1

                step_result["completed_at"] = time.time()
                execution_results.append(step_result)

            # Update plan with execution results
            plan["execution_results"] = execution_results
            plan["executed_at"] = time.time()

            result = {
                "success": True,
                "plan_id": plan_id,
                "execute_steps": execute_steps,
                "total_steps": len(steps),
                "completed_steps": completed_steps,
                "failed_steps": failed_steps,
                "success_rate": completed_steps / len(steps) if steps else 0,
                "execution_results": execution_results
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to execute multi-step loop: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in multi-step loop: {e}")
            return {
                "success": False,
                "error": error_msg,
                "plan_id": plan_id
            }

    def context_aware_refactor(self, file_path: str, refactor_type: str = "general",
                              target_language: Optional[str] = None) -> Dict[str, Any]:
        """Smart code refactoring with context awareness.

        Args:
            file_path: Path to the file to refactor.
            refactor_type: Type of refactoring ('general', 'performance', 'readability', 'structure').
            target_language: Target language for conversion (optional).

        Returns:
            Result dictionary with refactoring results.
        """
        try:
            self.history.append({
                "action": "context_aware_refactor",
                "file_path": file_path,
                "refactor_type": refactor_type,
                "target_language": target_language,
                "timestamp": time.time()
            })

            full_path = self.workspace_dir / file_path
            if not full_path.exists():
                return {
                    "success": False,
                    "error": f"File not found: {file_path}"
                }

            # Read original content
            original_content = full_path.read_text(encoding='utf-8')

            # Analyze the code
            analysis = self._analyze_code_for_refactoring(original_content, full_path.suffix)

            # Perform refactoring based on type
            refactored_content = self._perform_refactoring(original_content, refactor_type,
                                                         analysis, target_language)

            # Create backup
            backup_path = full_path.with_suffix(full_path.suffix + '.backup')
            backup_path.write_text(original_content, encoding='utf-8')

            # Write refactored content
            full_path.write_text(refactored_content, encoding='utf-8')

            result = {
                "success": True,
                "file_path": file_path,
                "refactor_type": refactor_type,
                "target_language": target_language,
                "original_lines": len(original_content.split('\n')),
                "refactored_lines": len(refactored_content.split('\n')),
                "backup_created": str(backup_path),
                "analysis": analysis,
                "changes_made": self._compare_code(original_content, refactored_content)
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to refactor code: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in refactoring: {e}")
            return {
                "success": False,
                "error": error_msg,
                "file_path": file_path
            }

    def _analyze_code_for_refactoring(self, content: str, file_extension: str) -> Dict[str, Any]:
        """Analyze code to identify refactoring opportunities."""
        analysis = {
            "language": "unknown",
            "issues": [],
            "complexity": "medium",
            "suggestions": []
        }

        # Determine language
        lang_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c'
        }
        analysis["language"] = lang_map.get(file_extension, "unknown")

        lines = content.split('\n')

        # General code analysis
        for line_num, line in enumerate(lines, 1):
            line_stripped = line.strip()

            # Long lines
            if len(line) > 120:
                analysis["issues"].append({
                    "type": "long_line",
                    "line": line_num,
                    "message": f"Line too long ({len(line)} characters)"
                })

            # Nested complexity (basic check)
            indent_level = len(line) - len(line.lstrip())
            if indent_level > 16:  # More than 4 levels of nesting
                analysis["issues"].append({
                    "type": "deep_nesting",
                    "line": line_num,
                    "message": "Deep nesting detected"
                })

        # Language-specific analysis
        if analysis["language"] == "python":
            analysis.update(self._analyze_python_code(content))
        elif analysis["language"] == "javascript":
            analysis.update(self._analyze_javascript_code(content))

        return analysis

    def _analyze_python_code(self, content: str) -> Dict[str, Any]:
        """Analyze Python-specific code issues."""
        issues = []
        suggestions = []

        try:
            tree = ast.parse(content)

            # Count functions and classes
            functions = [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
            classes = [node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]

            if len(functions) > 20:
                suggestions.append("Consider splitting into multiple modules")

            if len(classes) > 10:
                suggestions.append("Consider organizing classes into separate files")

            # Check for long functions
            for func in functions:
                func_lines = func.end_lineno - func.lineno if hasattr(func, 'end_lineno') else 0
                if func_lines > 50:
                    issues.append({
                        "type": "long_function",
                        "line": func.lineno,
                        "message": f"Function '{func.name}' is too long ({func_lines} lines)"
                    })

        except SyntaxError:
            issues.append({
                "type": "syntax_error",
                "line": 0,
                "message": "Syntax error in Python code"
            })

        return {"issues": issues, "suggestions": suggestions}

    def _analyze_javascript_code(self, content: str) -> Dict[str, Any]:
        """Analyze JavaScript-specific code issues."""
        issues = []
        suggestions = []

        lines = content.split('\n')

        for line_num, line in enumerate(lines, 1):
            line_stripped = line.strip()

            # Check for var usage
            if line_stripped.startswith('var '):
                issues.append({
                    "type": "var_usage",
                    "line": line_num,
                    "message": "Use 'let' or 'const' instead of 'var'"
                })

            # Check for == instead of ===
            if ' == ' in line_stripped and ' === ' not in line_stripped:
                issues.append({
                    "type": "loose_equality",
                    "line": line_num,
                    "message": "Use strict equality (===) instead of loose equality (==)"
                })

        return {"issues": issues, "suggestions": suggestions}

    def _perform_refactoring(self, content: str, refactor_type: str, analysis: Dict[str, Any],
                           target_language: Optional[str] = None) -> str:
        """Perform the actual refactoring based on analysis."""
        refactored_content = content

        if refactor_type == "readability":
            # Improve readability
            lines = refactored_content.split('\n')
            improved_lines = []

            for line in lines:
                # Remove trailing whitespace
                improved_line = line.rstrip()

                # Fix basic formatting issues
                if analysis["language"] == "python":
                    # Add space after commas
                    improved_line = re.sub(r',(\S)', r', \1', improved_line)
                    # Add space around operators
                    improved_line = re.sub(r'(\w)=(\w)', r'\1 = \2', improved_line)

                improved_lines.append(improved_line)

            refactored_content = '\n'.join(improved_lines)

        elif refactor_type == "performance":
            # Basic performance improvements
            if analysis["language"] == "python":
                # Replace list comprehensions where appropriate
                refactored_content = re.sub(
                    r'for\s+(\w+)\s+in\s+(.+):\s*\n\s*(.+)\.append\((.+)\)',
                    r'[\4 for \1 in \2]',
                    refactored_content
                )

        return refactored_content

    def _compare_code(self, original: str, refactored: str) -> Dict[str, Any]:
        """Compare original and refactored code to identify changes."""
        import difflib

        original_lines = original.split('\n')
        refactored_lines = refactored.split('\n')

        diff = list(difflib.unified_diff(original_lines, refactored_lines,
                                       fromfile='original', tofile='refactored', lineterm=''))

        changes = {
            "total_changes": len([line for line in diff if line.startswith(('+', '-'))]),
            "additions": len([line for line in diff if line.startswith('+') and not line.startswith('+++')]),
            "deletions": len([line for line in diff if line.startswith('-') and not line.startswith('---')]),
            "diff": diff[:50]  # Limit diff output
        }

        return changes

    def context_compression(self, context_data: str, max_length: int = 1000,
                          compression_type: str = "summary") -> Dict[str, Any]:
        """Compress context while preserving important information.

        Args:
            context_data: The context data to compress.
            max_length: Maximum length of compressed context.
            compression_type: Type of compression ('summary', 'keywords', 'structured').

        Returns:
            Result dictionary with compressed context.
        """
        try:
            self.history.append({
                "action": "context_compression",
                "original_length": len(context_data),
                "max_length": max_length,
                "compression_type": compression_type,
                "timestamp": time.time()
            })

            compressed_context = ""

            if compression_type == "summary":
                # Extract key sentences
                sentences = context_data.split('.')
                important_sentences = []

                # Simple importance scoring based on keywords
                important_keywords = ['error', 'function', 'class', 'import', 'def', 'return', 'if', 'for', 'while']

                for sentence in sentences:
                    score = sum(1 for keyword in important_keywords if keyword in sentence.lower())
                    if score > 0:
                        important_sentences.append((sentence.strip(), score))

                # Sort by importance and take top sentences
                important_sentences.sort(key=lambda x: x[1], reverse=True)

                compressed_context = '. '.join([sent[0] for sent in important_sentences])
                if len(compressed_context) > max_length:
                    compressed_context = compressed_context[:max_length] + "..."

            elif compression_type == "keywords":
                # Extract keywords and key phrases
                words = context_data.split()
                important_words = []

                # Filter out common words and keep important ones
                common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}

                for word in words:
                    clean_word = re.sub(r'[^\w]', '', word.lower())
                    if len(clean_word) > 3 and clean_word not in common_words:
                        important_words.append(clean_word)

                # Remove duplicates and take most frequent
                from collections import Counter
                word_counts = Counter(important_words)
                top_words = [word for word, count in word_counts.most_common(50)]

                compressed_context = ', '.join(top_words)
                if len(compressed_context) > max_length:
                    compressed_context = compressed_context[:max_length] + "..."

            elif compression_type == "structured":
                # Create structured summary
                lines = context_data.split('\n')
                structure = {
                    "functions": [],
                    "classes": [],
                    "imports": [],
                    "errors": [],
                    "other": []
                }

                for line in lines:
                    line_stripped = line.strip()
                    if line_stripped.startswith('def '):
                        structure["functions"].append(line_stripped)
                    elif line_stripped.startswith('class '):
                        structure["classes"].append(line_stripped)
                    elif 'import' in line_stripped:
                        structure["imports"].append(line_stripped)
                    elif any(word in line_stripped.lower() for word in ['error', 'exception', 'fail']):
                        structure["errors"].append(line_stripped)
                    elif line_stripped:
                        structure["other"].append(line_stripped)

                # Build compressed context
                parts = []
                for category, items in structure.items():
                    if items:
                        parts.append(f"{category.upper()}: {'; '.join(items[:5])}")

                compressed_context = ' | '.join(parts)
                if len(compressed_context) > max_length:
                    compressed_context = compressed_context[:max_length] + "..."

            result = {
                "success": True,
                "original_length": len(context_data),
                "compressed_length": len(compressed_context),
                "compression_ratio": len(compressed_context) / len(context_data) if context_data else 0,
                "compression_type": compression_type,
                "compressed_context": compressed_context
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to compress context: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in context compression: {e}")
            return {
                "success": False,
                "error": error_msg
            }

    def context_tracking_memory(self, context_id: str, context_data: Dict[str, Any],
                              action: str = "store") -> Dict[str, Any]:
        """Track and manage context memory across sessions.

        Args:
            context_id: Unique identifier for the context.
            context_data: Context data to store/retrieve.
            action: Action to perform ('store', 'retrieve', 'update', 'delete').

        Returns:
            Result dictionary with memory operation results.
        """
        try:
            self.history.append({
                "action": "context_tracking_memory",
                "context_id": context_id,
                "memory_action": action,
                "timestamp": time.time()
            })

            if action == "store":
                self.context_memory[context_id] = {
                    "data": context_data,
                    "created_at": time.time(),
                    "updated_at": time.time(),
                    "access_count": 0
                }
                result = {
                    "success": True,
                    "action": "store",
                    "context_id": context_id,
                    "message": "Context stored successfully"
                }

            elif action == "retrieve":
                if context_id in self.context_memory:
                    self.context_memory[context_id]["access_count"] += 1
                    result = {
                        "success": True,
                        "action": "retrieve",
                        "context_id": context_id,
                        "context_data": self.context_memory[context_id]["data"],
                        "metadata": {
                            "created_at": self.context_memory[context_id]["created_at"],
                            "updated_at": self.context_memory[context_id]["updated_at"],
                            "access_count": self.context_memory[context_id]["access_count"]
                        }
                    }
                else:
                    result = {
                        "success": False,
                        "action": "retrieve",
                        "context_id": context_id,
                        "error": "Context not found"
                    }

            elif action == "update":
                if context_id in self.context_memory:
                    self.context_memory[context_id]["data"].update(context_data)
                    self.context_memory[context_id]["updated_at"] = time.time()
                    result = {
                        "success": True,
                        "action": "update",
                        "context_id": context_id,
                        "message": "Context updated successfully"
                    }
                else:
                    result = {
                        "success": False,
                        "action": "update",
                        "context_id": context_id,
                        "error": "Context not found"
                    }

            elif action == "delete":
                if context_id in self.context_memory:
                    del self.context_memory[context_id]
                    result = {
                        "success": True,
                        "action": "delete",
                        "context_id": context_id,
                        "message": "Context deleted successfully"
                    }
                else:
                    result = {
                        "success": False,
                        "action": "delete",
                        "context_id": context_id,
                        "error": "Context not found"
                    }

            else:
                result = {
                    "success": False,
                    "error": f"Unknown action: {action}"
                }

            self.history[-1]["success"] = result["success"]
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to manage context memory: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in context memory management: {e}")
            return {
                "success": False,
                "error": error_msg,
                "context_id": context_id
            }

    def get_history(self) -> List[Dict[str, Any]]:
        """Get the workflow tool operation history."""
        return self.history
