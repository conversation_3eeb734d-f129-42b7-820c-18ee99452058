"""
Advanced Execution Features Tool for the AI Agent.
Implements step-by-step execution, result analysis, iterative planning, and advanced execution capabilities.
"""

import os
import re
import ast
import json
import time
import logging
import threading
import asyncio
import concurrent.futures
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Set, Callable
import hashlib
import queue
import subprocess
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ExecutionStatus(Enum):
    """Execution status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ExecutionStep:
    """Represents a single execution step."""
    step_id: str
    description: str
    command: str
    dependencies: List[str]
    status: ExecutionStatus
    result: Optional[Dict[str, Any]] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error: Optional[str] = None

class AdvancedExecutionTool:
    """Advanced tool for sophisticated execution features."""

    def __init__(self, workspace_dir: Path):
        """Initialize the advanced execution tool.

        Args:
            workspace_dir: The workspace directory to use.
        """
        self.workspace_dir = workspace_dir
        self.history: List[Dict[str, Any]] = []
        
        # Execution management
        self.execution_plans = {}
        self.active_executions = {}
        self.execution_results = {}
        
        # Threading and async support
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=4)
        self.execution_queue = queue.Queue()
        
        # Analysis and optimization
        self.performance_metrics = {}
        self.optimization_cache = {}
        
        # Context and state management
        self.execution_context = {}
        self.state_snapshots = {}

    def step_by_step_execution(self, plan: Dict[str, Any], 
                              interactive: bool = False) -> Dict[str, Any]:
        """Execute a plan step by step with detailed tracking.

        Args:
            plan: Execution plan with steps and dependencies.
            interactive: Whether to pause for user confirmation between steps.

        Returns:
            Result dictionary with execution details.
        """
        try:
            self.history.append({
                "action": "step_by_step_execution",
                "plan": plan,
                "interactive": interactive,
                "timestamp": time.time()
            })
            
            # Generate execution ID
            execution_id = hashlib.md5(f"{json.dumps(plan)}{time.time()}".encode()).hexdigest()[:8]
            
            # Parse and validate plan
            steps = self._parse_execution_plan(plan)
            
            # Create execution context
            execution_context = {
                "execution_id": execution_id,
                "total_steps": len(steps),
                "completed_steps": 0,
                "failed_steps": 0,
                "start_time": time.time(),
                "interactive": interactive,
                "status": ExecutionStatus.RUNNING
            }
            
            self.active_executions[execution_id] = execution_context
            
            # Execute steps in dependency order
            execution_results = []
            dependency_graph = self._build_dependency_graph(steps)
            execution_order = self._topological_sort(dependency_graph)
            
            for step_id in execution_order:
                step = next(s for s in steps if s.step_id == step_id)
                
                # Check if dependencies are satisfied
                if not self._dependencies_satisfied(step, execution_results):
                    step.status = ExecutionStatus.FAILED
                    step.error = "Dependencies not satisfied"
                    execution_context["failed_steps"] += 1
                    continue
                
                # Interactive confirmation
                if interactive:
                    confirmation = self._request_step_confirmation(step)
                    if not confirmation:
                        step.status = ExecutionStatus.CANCELLED
                        continue
                
                # Execute step
                step_result = self._execute_single_step(step, execution_context)
                execution_results.append(step_result)
                
                if step_result["success"]:
                    execution_context["completed_steps"] += 1
                else:
                    execution_context["failed_steps"] += 1
            
            # Finalize execution
            execution_context["end_time"] = time.time()
            execution_context["duration"] = execution_context["end_time"] - execution_context["start_time"]
            execution_context["status"] = ExecutionStatus.COMPLETED
            
            result = {
                "success": True,
                "execution_id": execution_id,
                "execution_context": execution_context,
                "step_results": execution_results,
                "summary": self._generate_execution_summary(execution_context, execution_results)
            }
            
            self.execution_results[execution_id] = result
            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result
            
        except Exception as e:
            error_msg = f"Failed to execute step-by-step plan: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in step-by-step execution: {e}")
            return {
                "success": False,
                "error": error_msg
            }

    def _parse_execution_plan(self, plan: Dict[str, Any]) -> List[ExecutionStep]:
        """Parse execution plan into ExecutionStep objects."""
        steps = []
        
        for step_data in plan.get("steps", []):
            step = ExecutionStep(
                step_id=step_data.get("id", f"step_{len(steps)}"),
                description=step_data.get("description", ""),
                command=step_data.get("command", ""),
                dependencies=step_data.get("dependencies", []),
                status=ExecutionStatus.PENDING
            )
            steps.append(step)
        
        return steps

    def _build_dependency_graph(self, steps: List[ExecutionStep]) -> Dict[str, List[str]]:
        """Build dependency graph from steps."""
        graph = {}
        for step in steps:
            graph[step.step_id] = step.dependencies
        return graph

    def _topological_sort(self, graph: Dict[str, List[str]]) -> List[str]:
        """Perform topological sort on dependency graph."""
        # Simple topological sort implementation
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(node):
            if node in temp_visited:
                raise ValueError("Circular dependency detected")
            if node not in visited:
                temp_visited.add(node)
                for dependency in graph.get(node, []):
                    visit(dependency)
                temp_visited.remove(node)
                visited.add(node)
                result.append(node)
        
        for node in graph:
            if node not in visited:
                visit(node)
        
        return result

    def _dependencies_satisfied(self, step: ExecutionStep, 
                               completed_results: List[Dict[str, Any]]) -> bool:
        """Check if step dependencies are satisfied."""
        completed_step_ids = {r["step_id"] for r in completed_results if r["success"]}
        return all(dep in completed_step_ids for dep in step.dependencies)

    def _request_step_confirmation(self, step: ExecutionStep) -> bool:
        """Request user confirmation for step execution (simulated)."""
        # In a real implementation, this would prompt the user
        # For now, we'll simulate confirmation
        logger.info(f"Requesting confirmation for step: {step.description}")
        return True  # Auto-confirm for simulation

    def _execute_single_step(self, step: ExecutionStep, 
                           context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single step."""
        step.start_time = time.time()
        step.status = ExecutionStatus.RUNNING
        
        try:
            # Execute the command
            if step.command.startswith("shell:"):
                # Shell command
                cmd = step.command[6:]  # Remove "shell:" prefix
                result = subprocess.run(cmd, shell=True, capture_output=True, 
                                      text=True, cwd=self.workspace_dir)
                
                step_result = {
                    "step_id": step.step_id,
                    "success": result.returncode == 0,
                    "output": result.stdout,
                    "error": result.stderr,
                    "return_code": result.returncode
                }
            
            elif step.command.startswith("python:"):
                # Python code execution
                code = step.command[7:]  # Remove "python:" prefix
                step_result = self._execute_python_code(code, step.step_id)
            
            else:
                # Generic command execution
                step_result = {
                    "step_id": step.step_id,
                    "success": True,
                    "output": f"Executed: {step.command}",
                    "command": step.command
                }
            
            step.status = ExecutionStatus.COMPLETED if step_result["success"] else ExecutionStatus.FAILED
            step.result = step_result
            
        except Exception as e:
            step.status = ExecutionStatus.FAILED
            step.error = str(e)
            step_result = {
                "step_id": step.step_id,
                "success": False,
                "error": str(e)
            }
        
        step.end_time = time.time()
        return step_result

    def _execute_python_code(self, code: str, step_id: str) -> Dict[str, Any]:
        """Execute Python code safely."""
        try:
            # Create a restricted execution environment
            exec_globals = {
                "__builtins__": {
                    "print": print,
                    "len": len,
                    "str": str,
                    "int": int,
                    "float": float,
                    "list": list,
                    "dict": dict,
                    "range": range,
                    "enumerate": enumerate,
                    "zip": zip
                }
            }
            exec_locals = {}
            
            # Execute the code
            exec(code, exec_globals, exec_locals)
            
            return {
                "step_id": step_id,
                "success": True,
                "output": "Python code executed successfully",
                "locals": {k: str(v) for k, v in exec_locals.items() if not k.startswith('_')}
            }
            
        except Exception as e:
            return {
                "step_id": step_id,
                "success": False,
                "error": str(e)
            }

    def _generate_execution_summary(self, context: Dict[str, Any], 
                                  results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate execution summary."""
        return {
            "total_steps": context["total_steps"],
            "completed_steps": context["completed_steps"],
            "failed_steps": context["failed_steps"],
            "success_rate": context["completed_steps"] / context["total_steps"] if context["total_steps"] > 0 else 0,
            "duration": context.get("duration", 0),
            "status": context["status"].value
        }

    def result_analysis(self, execution_id: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """Analyze execution results with detailed insights.

        Args:
            execution_id: ID of the execution to analyze.
            analysis_type: Type of analysis ('comprehensive', 'performance', 'errors').

        Returns:
            Result dictionary with analysis insights.
        """
        try:
            self.history.append({
                "action": "result_analysis",
                "execution_id": execution_id,
                "analysis_type": analysis_type,
                "timestamp": time.time()
            })

            if execution_id not in self.execution_results:
                return {
                    "success": False,
                    "error": f"Execution ID '{execution_id}' not found"
                }

            execution_data = self.execution_results[execution_id]

            # Perform different types of analysis
            analysis_results = {}

            if analysis_type in ["comprehensive", "performance"]:
                analysis_results["performance"] = self._analyze_performance(execution_data)

            if analysis_type in ["comprehensive", "errors"]:
                analysis_results["errors"] = self._analyze_errors(execution_data)

            if analysis_type == "comprehensive":
                analysis_results["patterns"] = self._analyze_patterns(execution_data)
                analysis_results["optimization"] = self._suggest_optimizations(execution_data)

            # Generate insights and recommendations
            insights = self._generate_insights(analysis_results, execution_data)

            result = {
                "success": True,
                "execution_id": execution_id,
                "analysis_type": analysis_type,
                "analysis_results": analysis_results,
                "insights": insights,
                "recommendations": self._generate_recommendations(analysis_results)
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to analyze execution results: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in result analysis: {e}")
            return {
                "success": False,
                "error": error_msg,
                "execution_id": execution_id
            }

    def _analyze_performance(self, execution_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze performance metrics of execution."""
        step_results = execution_data.get("step_results", [])
        context = execution_data.get("execution_context", {})

        performance_metrics = {
            "total_duration": context.get("duration", 0),
            "average_step_time": 0,
            "slowest_step": None,
            "fastest_step": None,
            "throughput": 0
        }

        if step_results:
            # Calculate step timings (simulated since we don't have actual timing data)
            step_times = []
            for i, result in enumerate(step_results):
                # Simulate step timing based on success/failure
                estimated_time = 2.0 if result.get("success", False) else 5.0
                step_times.append(estimated_time)

            performance_metrics["average_step_time"] = sum(step_times) / len(step_times)
            performance_metrics["slowest_step"] = max(step_times)
            performance_metrics["fastest_step"] = min(step_times)
            performance_metrics["throughput"] = len(step_results) / context.get("duration", 1)

        return performance_metrics

    def _analyze_errors(self, execution_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze errors and failures in execution."""
        step_results = execution_data.get("step_results", [])

        error_analysis = {
            "total_errors": 0,
            "error_types": {},
            "error_patterns": [],
            "critical_failures": []
        }

        for result in step_results:
            if not result.get("success", True):
                error_analysis["total_errors"] += 1

                error_msg = result.get("error", "Unknown error")

                # Categorize error types
                if "permission" in error_msg.lower():
                    error_analysis["error_types"]["permission"] = error_analysis["error_types"].get("permission", 0) + 1
                elif "not found" in error_msg.lower():
                    error_analysis["error_types"]["not_found"] = error_analysis["error_types"].get("not_found", 0) + 1
                elif "syntax" in error_msg.lower():
                    error_analysis["error_types"]["syntax"] = error_analysis["error_types"].get("syntax", 0) + 1
                else:
                    error_analysis["error_types"]["other"] = error_analysis["error_types"].get("other", 0) + 1

                # Identify critical failures
                if result.get("return_code", 0) != 0:
                    error_analysis["critical_failures"].append({
                        "step_id": result.get("step_id", "unknown"),
                        "error": error_msg,
                        "return_code": result.get("return_code", 0)
                    })

        return error_analysis

    def _analyze_patterns(self, execution_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze execution patterns."""
        step_results = execution_data.get("step_results", [])

        patterns = {
            "success_patterns": [],
            "failure_patterns": [],
            "dependency_issues": [],
            "resource_usage": "normal"
        }

        # Analyze success/failure sequences
        success_sequence = [r.get("success", False) for r in step_results]

        # Find consecutive failures
        consecutive_failures = 0
        max_consecutive_failures = 0
        for success in success_sequence:
            if not success:
                consecutive_failures += 1
                max_consecutive_failures = max(max_consecutive_failures, consecutive_failures)
            else:
                consecutive_failures = 0

        if max_consecutive_failures > 2:
            patterns["failure_patterns"].append(f"Up to {max_consecutive_failures} consecutive failures detected")

        return patterns

    def _suggest_optimizations(self, execution_data: Dict[str, Any]) -> List[str]:
        """Suggest optimizations based on execution analysis."""
        suggestions = []
        context = execution_data.get("execution_context", {})

        # Performance-based suggestions
        if context.get("duration", 0) > 60:  # More than 1 minute
            suggestions.append("Consider parallelizing independent steps to reduce execution time")

        if context.get("failed_steps", 0) > 0:
            suggestions.append("Add error handling and retry logic for failed steps")

        # Success rate based suggestions
        success_rate = context.get("completed_steps", 0) / context.get("total_steps", 1)
        if success_rate < 0.8:
            suggestions.append("Review step dependencies and prerequisites")

        return suggestions

    def _generate_insights(self, analysis_results: Dict[str, Any],
                         execution_data: Dict[str, Any]) -> List[str]:
        """Generate insights from analysis results."""
        insights = []

        # Performance insights
        if "performance" in analysis_results:
            perf = analysis_results["performance"]
            if perf.get("average_step_time", 0) > 5:
                insights.append("Steps are taking longer than expected - consider optimization")

        # Error insights
        if "errors" in analysis_results:
            errors = analysis_results["errors"]
            if errors.get("total_errors", 0) > 0:
                insights.append(f"Execution had {errors['total_errors']} errors - review error handling")

        return insights

    def _generate_recommendations(self, analysis_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis."""
        recommendations = []

        if "optimization" in analysis_results:
            recommendations.extend(analysis_results["optimization"])

        # Add general recommendations
        recommendations.append("Monitor execution metrics for continuous improvement")
        recommendations.append("Implement proper logging for better debugging")

        return recommendations

    def iterative_planning(self, initial_goal: str, max_iterations: int = 5,
                         adaptation_threshold: float = 0.7) -> Dict[str, Any]:
        """Implement iterative planning with adaptive refinement.

        Args:
            initial_goal: The initial goal to achieve.
            max_iterations: Maximum number of planning iterations.
            adaptation_threshold: Threshold for plan adaptation (0.0-1.0).

        Returns:
            Result dictionary with iterative planning results.
        """
        try:
            self.history.append({
                "action": "iterative_planning",
                "initial_goal": initial_goal,
                "max_iterations": max_iterations,
                "adaptation_threshold": adaptation_threshold,
                "timestamp": time.time()
            })

            planning_id = hashlib.md5(f"{initial_goal}{time.time()}".encode()).hexdigest()[:8]

            iterations = []
            current_goal = initial_goal
            current_context = {"iteration": 0, "previous_results": []}

            for iteration in range(max_iterations):
                iteration_start = time.time()

                # Generate plan for current iteration
                plan = self._generate_iterative_plan(current_goal, current_context)

                # Evaluate plan quality
                plan_quality = self._evaluate_plan_quality(plan, current_context)

                # Execute plan if quality is acceptable
                if plan_quality >= adaptation_threshold:
                    execution_result = self._simulate_plan_execution(plan)
                else:
                    execution_result = {"success": False, "reason": "Plan quality below threshold"}

                # Analyze results and adapt
                adaptation = self._adapt_plan(plan, execution_result, current_goal)

                iteration_result = {
                    "iteration": iteration + 1,
                    "plan": plan,
                    "plan_quality": plan_quality,
                    "execution_result": execution_result,
                    "adaptation": adaptation,
                    "duration": time.time() - iteration_start
                }

                iterations.append(iteration_result)

                # Update context for next iteration
                current_context["iteration"] = iteration + 1
                current_context["previous_results"].append(execution_result)

                # Check if goal is achieved
                if execution_result.get("success", False) and execution_result.get("goal_achieved", False):
                    break

                # Adapt goal if necessary
                if adaptation.get("goal_refinement"):
                    current_goal = adaptation["goal_refinement"]

            result = {
                "success": True,
                "planning_id": planning_id,
                "initial_goal": initial_goal,
                "final_goal": current_goal,
                "total_iterations": len(iterations),
                "iterations": iterations,
                "final_success": iterations[-1]["execution_result"].get("success", False) if iterations else False
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to perform iterative planning: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in iterative planning: {e}")
            return {
                "success": False,
                "error": error_msg,
                "initial_goal": initial_goal
            }

    def _generate_iterative_plan(self, goal: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a plan for the current iteration."""
        iteration = context.get("iteration", 0)
        previous_results = context.get("previous_results", [])

        # Analyze previous failures to improve plan
        failed_approaches = []
        for result in previous_results:
            if not result.get("success", False):
                failed_approaches.append(result.get("approach", "unknown"))

        # Generate plan based on goal and learning from previous iterations
        plan = {
            "goal": goal,
            "iteration": iteration,
            "approach": self._select_approach(goal, failed_approaches),
            "steps": self._generate_plan_steps(goal, iteration, failed_approaches),
            "estimated_duration": self._estimate_plan_duration(goal),
            "risk_factors": self._identify_risk_factors(goal, previous_results)
        }

        return plan

    def _select_approach(self, goal: str, failed_approaches: List[str]) -> str:
        """Select the best approach for the goal, avoiding failed ones."""
        goal_lower = goal.lower()

        # Define possible approaches
        approaches = []
        if any(word in goal_lower for word in ['build', 'create', 'develop']):
            approaches = ["incremental", "prototype", "test_driven", "modular"]
        elif any(word in goal_lower for word in ['fix', 'debug', 'resolve']):
            approaches = ["systematic", "binary_search", "logging", "reproduction"]
        elif any(word in goal_lower for word in ['optimize', 'improve', 'enhance']):
            approaches = ["profiling", "benchmarking", "refactoring", "caching"]
        else:
            approaches = ["systematic", "iterative", "experimental"]

        # Filter out failed approaches
        available_approaches = [a for a in approaches if a not in failed_approaches]

        # Return best available approach
        return available_approaches[0] if available_approaches else "fallback"

    def _generate_plan_steps(self, goal: str, iteration: int, failed_approaches: List[str]) -> List[Dict[str, Any]]:
        """Generate steps for the plan."""
        steps = []

        # Add analysis step
        steps.append({
            "id": "analyze",
            "description": f"Analyze requirements for: {goal}",
            "command": "analyze_requirements",
            "dependencies": []
        })

        # Add implementation steps based on goal
        if "build" in goal.lower():
            steps.extend([
                {
                    "id": "design",
                    "description": "Design solution architecture",
                    "command": "design_solution",
                    "dependencies": ["analyze"]
                },
                {
                    "id": "implement",
                    "description": "Implement core functionality",
                    "command": "implement_core",
                    "dependencies": ["design"]
                },
                {
                    "id": "test",
                    "description": "Test implementation",
                    "command": "run_tests",
                    "dependencies": ["implement"]
                }
            ])

        # Add validation step
        steps.append({
            "id": "validate",
            "description": "Validate results",
            "command": "validate_results",
            "dependencies": [s["id"] for s in steps if s["id"] != "validate"]
        })

        return steps

    def _estimate_plan_duration(self, goal: str) -> float:
        """Estimate plan execution duration."""
        # Simple heuristic based on goal complexity
        goal_words = len(goal.split())
        base_duration = 30.0  # 30 seconds base

        if goal_words > 10:
            return base_duration * 2
        elif goal_words > 5:
            return base_duration * 1.5
        else:
            return base_duration

    def _identify_risk_factors(self, goal: str, previous_results: List[Dict[str, Any]]) -> List[str]:
        """Identify risk factors for the plan."""
        risks = []

        # Analyze previous failures
        failure_count = sum(1 for r in previous_results if not r.get("success", False))
        if failure_count > 2:
            risks.append("High failure rate in previous iterations")

        # Goal complexity risks
        if len(goal.split()) > 15:
            risks.append("Complex goal may require decomposition")

        # Technical risks based on goal content
        if any(word in goal.lower() for word in ['database', 'network', 'api']):
            risks.append("External dependencies may cause failures")

        return risks

    def _evaluate_plan_quality(self, plan: Dict[str, Any], context: Dict[str, Any]) -> float:
        """Evaluate the quality of a generated plan."""
        quality_score = 0.8  # Base quality

        # Check plan completeness
        if len(plan.get("steps", [])) < 3:
            quality_score -= 0.2

        # Check risk factors
        risk_count = len(plan.get("risk_factors", []))
        if risk_count > 3:
            quality_score -= 0.1 * risk_count

        # Consider previous iteration success
        previous_results = context.get("previous_results", [])
        if previous_results:
            recent_success_rate = sum(1 for r in previous_results[-3:] if r.get("success", False)) / min(3, len(previous_results))
            quality_score = (quality_score + recent_success_rate) / 2

        return max(0.0, min(1.0, quality_score))

    def _simulate_plan_execution(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate plan execution (for demonstration)."""
        # Simulate execution based on plan characteristics
        steps = plan.get("steps", [])
        risk_factors = plan.get("risk_factors", [])

        # Calculate success probability
        base_success_prob = 0.8
        risk_penalty = len(risk_factors) * 0.1
        success_prob = max(0.1, base_success_prob - risk_penalty)

        # Simulate execution
        import random
        success = random.random() < success_prob

        return {
            "success": success,
            "goal_achieved": success,
            "steps_completed": len(steps) if success else random.randint(1, len(steps)),
            "approach": plan.get("approach", "unknown"),
            "duration": plan.get("estimated_duration", 30.0) * (1.2 if not success else 1.0)
        }

    def _adapt_plan(self, plan: Dict[str, Any], execution_result: Dict[str, Any],
                   current_goal: str) -> Dict[str, Any]:
        """Adapt the plan based on execution results."""
        adaptation = {
            "changes_made": [],
            "goal_refinement": None,
            "approach_change": None
        }

        if not execution_result.get("success", False):
            # Plan failed, need adaptation
            adaptation["changes_made"].append("Increased error handling")
            adaptation["changes_made"].append("Added validation steps")

            # Consider changing approach
            current_approach = plan.get("approach", "unknown")
            if current_approach == "systematic":
                adaptation["approach_change"] = "experimental"
            elif current_approach == "incremental":
                adaptation["approach_change"] = "prototype"

            # Consider goal refinement for complex goals
            if len(current_goal.split()) > 10:
                adaptation["goal_refinement"] = f"Simplified version of: {current_goal}"

        return adaptation

    def multi_threading_execution(self, tasks: List[Dict[str, Any]],
                                 max_workers: int = 4) -> Dict[str, Any]:
        """Execute tasks using multi-threading for parallel processing.

        Args:
            tasks: List of tasks to execute in parallel.
            max_workers: Maximum number of worker threads.

        Returns:
            Result dictionary with multi-threading execution results.
        """
        try:
            self.history.append({
                "action": "multi_threading_execution",
                "task_count": len(tasks),
                "max_workers": max_workers,
                "timestamp": time.time()
            })

            execution_id = hashlib.md5(f"{json.dumps(tasks)}{time.time()}".encode()).hexdigest()[:8]

            # Execute tasks in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                future_to_task = {}
                for i, task in enumerate(tasks):
                    future = executor.submit(self._execute_task, task, i)
                    future_to_task[future] = task

                # Collect results
                results = []
                for future in concurrent.futures.as_completed(future_to_task):
                    task = future_to_task[future]
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        results.append({
                            "task_id": task.get("id", "unknown"),
                            "success": False,
                            "error": str(e)
                        })

            # Analyze results
            successful_tasks = sum(1 for r in results if r.get("success", False))

            result = {
                "success": True,
                "execution_id": execution_id,
                "total_tasks": len(tasks),
                "successful_tasks": successful_tasks,
                "failed_tasks": len(tasks) - successful_tasks,
                "success_rate": successful_tasks / len(tasks) if tasks else 0,
                "results": results
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to execute multi-threading tasks: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in multi-threading execution: {e}")
            return {
                "success": False,
                "error": error_msg
            }

    def _execute_task(self, task: Dict[str, Any], task_index: int) -> Dict[str, Any]:
        """Execute a single task in a thread."""
        task_id = task.get("id", f"task_{task_index}")
        command = task.get("command", "")

        try:
            # Simulate task execution
            import random
            execution_time = random.uniform(0.5, 3.0)
            time.sleep(execution_time)

            # Simulate success/failure
            success = random.random() > 0.2  # 80% success rate

            return {
                "task_id": task_id,
                "success": success,
                "execution_time": execution_time,
                "output": f"Task {task_id} completed" if success else f"Task {task_id} failed"
            }

        except Exception as e:
            return {
                "task_id": task_id,
                "success": False,
                "error": str(e)
            }

    def context_aware_auto_refactoring(self, code_files: List[str],
                                     refactoring_goals: List[str]) -> Dict[str, Any]:
        """Perform context-aware automatic refactoring.

        Args:
            code_files: List of code files to refactor.
            refactoring_goals: List of refactoring goals (e.g., 'performance', 'readability').

        Returns:
            Result dictionary with refactoring results.
        """
        try:
            self.history.append({
                "action": "context_aware_auto_refactoring",
                "code_files": code_files,
                "refactoring_goals": refactoring_goals,
                "timestamp": time.time()
            })

            refactoring_id = hashlib.md5(f"{json.dumps(code_files)}{time.time()}".encode()).hexdigest()[:8]

            refactoring_results = []

            for file_path in code_files:
                file_result = self._refactor_single_file(file_path, refactoring_goals)
                refactoring_results.append(file_result)

            # Aggregate results
            total_files = len(code_files)
            successful_refactorings = sum(1 for r in refactoring_results if r.get("success", False))

            result = {
                "success": True,
                "refactoring_id": refactoring_id,
                "total_files": total_files,
                "successful_refactorings": successful_refactorings,
                "failed_refactorings": total_files - successful_refactorings,
                "refactoring_goals": refactoring_goals,
                "results": refactoring_results,
                "summary": self._generate_refactoring_summary(refactoring_results)
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to perform auto-refactoring: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in auto-refactoring: {e}")
            return {
                "success": False,
                "error": error_msg
            }

    def _refactor_single_file(self, file_path: str, goals: List[str]) -> Dict[str, Any]:
        """Refactor a single file based on goals."""
        try:
            full_path = self.workspace_dir / file_path
            if not full_path.exists():
                return {
                    "file_path": file_path,
                    "success": False,
                    "error": "File not found"
                }

            # Read original content
            original_content = full_path.read_text(encoding='utf-8')

            # Apply refactoring based on goals
            refactored_content = original_content
            changes_made = []

            for goal in goals:
                if goal == "readability":
                    refactored_content, readability_changes = self._improve_readability(refactored_content)
                    changes_made.extend(readability_changes)
                elif goal == "performance":
                    refactored_content, performance_changes = self._improve_performance(refactored_content)
                    changes_made.extend(performance_changes)

            # Create backup and write refactored content
            backup_path = full_path.with_suffix(full_path.suffix + '.backup')
            backup_path.write_text(original_content, encoding='utf-8')
            full_path.write_text(refactored_content, encoding='utf-8')

            return {
                "file_path": file_path,
                "success": True,
                "changes_made": changes_made,
                "backup_created": str(backup_path),
                "original_lines": len(original_content.split('\n')),
                "refactored_lines": len(refactored_content.split('\n'))
            }

        except Exception as e:
            return {
                "file_path": file_path,
                "success": False,
                "error": str(e)
            }

    def _improve_readability(self, content: str) -> Tuple[str, List[str]]:
        """Improve code readability."""
        changes = []
        lines = content.split('\n')
        improved_lines = []

        for line in lines:
            improved_line = line

            # Remove trailing whitespace
            if line != line.rstrip():
                improved_line = line.rstrip()
                changes.append("Removed trailing whitespace")

            # Add space after commas (simple regex)
            if re.search(r',\S', improved_line):
                improved_line = re.sub(r',(\S)', r', \1', improved_line)
                changes.append("Added space after commas")

            improved_lines.append(improved_line)

        return '\n'.join(improved_lines), list(set(changes))

    def _improve_performance(self, content: str) -> Tuple[str, List[str]]:
        """Improve code performance."""
        changes = []
        # Simple performance improvements (placeholder)
        improved_content = content

        # Example: Replace inefficient string concatenation
        if '+=' in content and 'str' in content:
            changes.append("Identified potential string concatenation optimization")

        return improved_content, changes

    def _generate_refactoring_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary of refactoring results."""
        total_changes = sum(len(r.get("changes_made", [])) for r in results if r.get("success", False))

        return {
            "total_changes": total_changes,
            "files_processed": len(results),
            "successful_refactorings": sum(1 for r in results if r.get("success", False)),
            "common_changes": ["Improved readability", "Enhanced performance"]
        }

    def get_history(self) -> List[Dict[str, Any]]:
        """Get the advanced execution tool operation history."""
        return self.history
