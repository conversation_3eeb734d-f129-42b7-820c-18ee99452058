"""
File tool for the Advanced AI Agent.
Provides robust file operations with comprehensive error handling and optimized performance.
"""

import os
import shutil
import glob
import logging
import mimetypes
import hashlib
import ast
import re
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Set, Tuple, BinaryIO

# Get the logger
logger = logging.getLogger("advanced_ai_agent")

class FileToolError(Exception):
    """Base exception for FileTool errors."""
    pass

class FileReadError(FileToolError):
    """Exception raised when a file cannot be read."""
    pass

class FileWriteError(FileToolError):
    """Exception raised when a file cannot be written."""
    pass

class FileDeleteError(FileToolError):
    """Exception raised when a file cannot be deleted."""
    pass

class FileTool:
    """File tool for file operations with enhanced error handling and performance."""

    # Common binary file extensions
    BINARY_EXTENSIONS = {
        '.bin', '.exe', '.dll', '.so', '.dylib', '.zip', '.tar', '.gz', '.rar',
        '.7z', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.pdf', '.doc',
        '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.mp3', '.mp4', '.avi', '.mov',
        '.flv', '.wmv', '.wma', '.aac', '.wav', '.ogg', '.flac', '.webm', '.webp'
    }

    # Common text file encodings to try
    TEXT_ENCODINGS = ['utf-8', 'latin-1', 'cp1252', 'ascii']

    def __init__(self, workspace_dir: Optional[Path] = None):
        """Initialize the file tool.

        Args:
            workspace_dir: The workspace directory to use. If None, will use the current directory.
        """
        self.workspace_dir = workspace_dir or Path.cwd()
        self.history: List[Dict[str, Any]] = []

        # Initialize file cache
        self._file_cache: Dict[str, Tuple[str, str]] = {}  # path -> (content, hash)

    def read_file(self, path: Union[str, Path], use_cache: bool = True) -> str:
        """Read a file with robust error handling and caching.

        Args:
            path: The path to the file to read.
            use_cache: Whether to use the file cache. Default is True.

        Returns:
            The contents of the file.

        Raises:
            FileNotFoundError: If the file does not exist.
            IsADirectoryError: If the path points to a directory.
            FileReadError: If the file cannot be read.
        """
        # Add to history
        self.history.append({"action": "read", "path": str(path)})

        # Resolve the path
        file_path = self._resolve_path(path)
        file_path_str = str(file_path)

        # Check if the file exists
        if not file_path.exists():
            error = FileNotFoundError(f"File not found: {file_path}")
            self.history[-1]["error"] = str(error)
            raise error

        # Check if the file is a directory
        if file_path.is_dir():
            error = IsADirectoryError(f"Cannot read directory: {file_path}")
            self.history[-1]["error"] = str(error)
            raise error

        # Check cache first if enabled
        if use_cache and file_path_str in self._file_cache:
            cached_content, cached_hash = self._file_cache[file_path_str]

            # Check if file has changed by comparing hash
            current_hash = self._get_file_hash(file_path)
            if current_hash == cached_hash:
                logger.debug(f"Using cached content for {file_path}")
                self.history[-1]["cached"] = True
                return cached_content

        # Determine if this is likely a binary file
        is_binary = self._is_binary_file(file_path)

        if is_binary:
            logger.warning(f"Attempting to read binary file as text: {file_path}")
            self.history[-1]["binary"] = True

        # Try multiple encodings for text files
        if not is_binary:
            for encoding in self.TEXT_ENCODINGS:
                try:
                    with open(file_path, "r", encoding=encoding) as f:
                        content = f.read()

                    # Cache the content
                    if use_cache:
                        file_hash = self._get_file_hash(file_path)
                        self._file_cache[file_path_str] = (content, file_hash)

                    self.history[-1]["encoding"] = encoding
                    return content
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.error(f"Error reading file {file_path} with encoding {encoding}: {e}")

        # If all text encodings fail or it's a binary file, try reading as binary and decode as latin-1
        # This ensures we can at least get some content even from binary files
        try:
            with open(file_path, "rb") as f:
                binary_content = f.read()

            # For binary files, we'll decode with latin-1 which can handle any byte value
            content = binary_content.decode('latin-1')

            # Cache the content
            if use_cache:
                file_hash = self._get_file_hash(file_path)
                self._file_cache[file_path_str] = (content, file_hash)

            self.history[-1]["encoding"] = "binary/latin-1"
            return content

        except Exception as e:
            error = FileReadError(f"Failed to read file {file_path}: {e}")
            self.history[-1]["error"] = str(error)
            logger.error(f"Failed to read file {file_path}: {e}")
            raise error

    def write_file(self, path: Union[str, Path], content: str, encoding: str = "utf-8",
                 make_backup: bool = False) -> bool:
        """Write content to a file with robust error handling.

        Args:
            path: The path to the file to write.
            content: The content to write to the file.
            encoding: The encoding to use. Default is utf-8.
            make_backup: Whether to make a backup of the existing file. Default is False.

        Returns:
            True if the file was written successfully, False otherwise.

        Raises:
            FileWriteError: If the file cannot be written.
        """
        # Add to history
        self.history.append({
            "action": "write",
            "path": str(path),
            "encoding": encoding,
            "make_backup": make_backup
        })

        # Resolve the path
        file_path = self._resolve_path(path)
        file_path_str = str(file_path)

        # Log the file path and content for debugging
        logger.info(f"Writing to file: {file_path}")
        logger.debug(f"Content length: {len(content)} characters")

        try:
            # Create the parent directory if it doesn't exist
            parent_dir = file_path.parent
            if not parent_dir.exists():
                logger.info(f"Creating directory: {parent_dir}")
                parent_dir.mkdir(parents=True, exist_ok=True)

            # Make a backup if requested and the file exists
            if make_backup and file_path.exists():
                backup_path = file_path.with_suffix(file_path.suffix + ".bak")
                shutil.copy2(file_path, backup_path)
                self.history[-1]["backup_path"] = str(backup_path)
                logger.info(f"Created backup of {file_path} at {backup_path}")

            # Write the file with error handling for encoding issues
            try:
                with open(file_path, "w", encoding=encoding) as f:
                    f.write(content)
            except UnicodeEncodeError:
                # Try with a more permissive encoding
                logger.warning(f"Failed to write with {encoding}, trying with latin-1")
                with open(file_path, "w", encoding="latin-1", errors="replace") as f:
                    f.write(content)
                self.history[-1]["encoding"] = "latin-1"

            # Verify the file was created
            if file_path.exists():
                logger.info(f"File successfully written: {file_path}")
            else:
                logger.error(f"File not created despite no errors: {file_path}")
                # Try one more approach for Windows
                if os.name == 'nt':
                    logger.info(f"Trying alternative approach for Windows")
                    with open(str(file_path), "w", encoding="latin-1") as f:
                        f.write(content)

            # Update the cache
            file_hash = self._get_file_hash(file_path)
            self._file_cache[file_path_str] = (content, file_hash)

            self.history[-1]["success"] = True
            return True

        except Exception as e:
            error = FileWriteError(f"Failed to write file {file_path}: {e}")
            self.history[-1]["error"] = str(error)
            self.history[-1]["success"] = False
            logger.error(f"Failed to write file {file_path}: {e}")

            # Try an alternative approach if the first one fails
            try:
                logger.info(f"Trying fallback method to write file: {file_path}")
                # Use a different approach for Windows
                if os.name == 'nt':
                    # Try using subprocess to write the file
                    import subprocess
                    temp_file = os.path.join(os.environ.get('TEMP', '.'), 'temp_content.txt')
                    with open(temp_file, 'w', encoding='utf-8', errors='replace') as f:
                        f.write(content)

                    # Use copy command to move the file
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    subprocess.run(['copy', temp_file, str(file_path)], shell=True, check=True)
                    os.remove(temp_file)

                    logger.info(f"File successfully written using fallback method: {file_path}")
                    self.history[-1]["success"] = True
                    return True
            except Exception as alt_e:
                logger.error(f"Alternative approach also failed: {str(alt_e)}")

            # Re-raise the original exception
            raise error

    def append_file(self, path: Union[str, Path], content: str, encoding: str = "utf-8") -> bool:
        """Append content to a file with robust error handling.

        Args:
            path: The path to the file to append to.
            content: The content to append to the file.
            encoding: The encoding to use. Default is utf-8.

        Returns:
            True if the content was appended successfully, False otherwise.

        Raises:
            FileWriteError: If the file cannot be written.
        """
        # Add to history
        self.history.append({
            "action": "append",
            "path": str(path),
            "encoding": encoding
        })

        # Resolve the path
        file_path = self._resolve_path(path)
        file_path_str = str(file_path)

        # Log the file path and content for debugging
        logger.info(f"Appending to file: {file_path}")
        logger.debug(f"Content length: {len(content)} characters")

        try:
            # Create the parent directory if it doesn't exist
            parent_dir = file_path.parent
            if not parent_dir.exists():
                logger.info(f"Creating directory: {parent_dir}")
                parent_dir.mkdir(parents=True, exist_ok=True)

            # Append to the file with error handling for encoding issues
            try:
                with open(file_path, "a", encoding=encoding) as f:
                    f.write(content)
            except UnicodeEncodeError:
                # Try with a more permissive encoding
                logger.warning(f"Failed to append with {encoding}, trying with latin-1")
                with open(file_path, "a", encoding="latin-1", errors="replace") as f:
                    f.write(content)
                self.history[-1]["encoding"] = "latin-1"
            except FileNotFoundError:
                # If the file doesn't exist, create it
                logger.info(f"File not found, creating new file: {file_path}")
                with open(file_path, "w", encoding=encoding) as f:
                    f.write(content)

            # Update the cache if it exists
            if file_path_str in self._file_cache:
                cached_content, _ = self._file_cache[file_path_str]
                new_content = cached_content + content
                file_hash = self._get_file_hash(file_path)
                self._file_cache[file_path_str] = (new_content, file_hash)

            self.history[-1]["success"] = True
            return True

        except Exception as e:
            error = FileWriteError(f"Failed to append to file {file_path}: {e}")
            self.history[-1]["error"] = str(error)
            self.history[-1]["success"] = False
            logger.error(f"Failed to append to file {file_path}: {e}")

            # Try an alternative approach if the first one fails
            try:
                logger.info(f"Trying fallback method to append to file: {file_path}")
                # Use a different approach for Windows
                if os.name == 'nt':
                    # Try using a temporary file and then appending
                    import subprocess
                    temp_file = os.path.join(os.environ.get('TEMP', '.'), 'temp_content.txt')
                    with open(temp_file, 'w', encoding='utf-8', errors='replace') as f:
                        f.write(content)

                    # Create parent directory if needed
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)

                    # If file doesn't exist, create it
                    if not os.path.exists(file_path):
                        with open(file_path, 'w') as f:
                            pass

                    # Use type command to append content
                    subprocess.run(f'type {temp_file} >> {file_path}', shell=True, check=True)
                    os.remove(temp_file)

                    logger.info(f"File successfully appended using fallback method: {file_path}")
                    self.history[-1]["success"] = True
                    return True
            except Exception as alt_e:
                logger.error(f"Alternative approach also failed: {str(alt_e)}")

            # Re-raise the original exception
            raise error

    def delete_file(self, path: Union[str, Path], make_backup: bool = False) -> bool:
        """Delete a file with robust error handling.

        Args:
            path: The path to the file to delete.
            make_backup: Whether to make a backup before deleting. Default is False.

        Returns:
            True if the file was deleted successfully, False otherwise.

        Raises:
            FileNotFoundError: If the file does not exist.
            FileDeleteError: If the file cannot be deleted.
        """
        # Add to history
        self.history.append({
            "action": "delete",
            "path": str(path),
            "make_backup": make_backup
        })

        # Resolve the path
        file_path = self._resolve_path(path)
        file_path_str = str(file_path)

        try:
            # Check if the file exists
            if not file_path.exists():
                error = FileNotFoundError(f"File not found: {file_path}")
                self.history[-1]["error"] = str(error)
                self.history[-1]["success"] = False
                raise error

            # Make a backup if requested
            if make_backup:
                if file_path.is_dir():
                    backup_path = file_path.with_name(file_path.name + "_backup")
                    shutil.copytree(file_path, backup_path)
                else:
                    backup_path = file_path.with_suffix(file_path.suffix + ".bak")
                    shutil.copy2(file_path, backup_path)

                self.history[-1]["backup_path"] = str(backup_path)
                logger.info(f"Created backup of {file_path} at {backup_path}")

            # Delete the file or directory
            if file_path.is_dir():
                shutil.rmtree(file_path)
                logger.info(f"Deleted directory: {file_path}")
            else:
                file_path.unlink()
                logger.info(f"Deleted file: {file_path}")

            # Remove from cache if it exists
            if file_path_str in self._file_cache:
                del self._file_cache[file_path_str]

            self.history[-1]["success"] = True
            return True

        except FileNotFoundError as e:
            # Re-raise FileNotFoundError
            self.history[-1]["error"] = str(e)
            self.history[-1]["success"] = False
            raise

        except Exception as e:
            error = FileDeleteError(f"Failed to delete {file_path}: {e}")
            self.history[-1]["error"] = str(error)
            self.history[-1]["success"] = False
            logger.error(f"Failed to delete {file_path}: {e}")
            raise error

    def list_files(self, path: Union[str, Path] = ".",
                  recursive: bool = False,
                  include_hidden: bool = False,
                  pattern: Optional[str] = None) -> List[str]:
        """List files in a directory with enhanced options.

        Args:
            path: The path to the directory to list.
            recursive: Whether to list files recursively. Default is False.
            include_hidden: Whether to include hidden files. Default is False.
            pattern: Optional glob pattern to filter files. Default is None.

        Returns:
            A list of files in the directory.

        Raises:
            FileNotFoundError: If the directory does not exist.
            NotADirectoryError: If the path is not a directory.
        """
        # Add to history
        self.history.append({
            "action": "list",
            "path": str(path),
            "recursive": recursive,
            "include_hidden": include_hidden,
            "pattern": pattern
        })

        # Resolve the path
        dir_path = self._resolve_path(path)

        try:
            # Check if the directory exists
            if not dir_path.exists():
                error = FileNotFoundError(f"Directory not found: {dir_path}")
                self.history[-1]["error"] = str(error)
                raise error

            # Check if the path is a directory
            if not dir_path.is_dir():
                error = NotADirectoryError(f"Not a directory: {dir_path}")
                self.history[-1]["error"] = str(error)
                raise error

            # List the files
            if recursive:
                if pattern:
                    # Use glob for pattern matching with recursion
                    glob_pattern = f"{dir_path}/**/{pattern}" if pattern else f"{dir_path}/**/*"
                    files = [Path(p) for p in glob.glob(glob_pattern, recursive=True)]
                else:
                    # Walk the directory tree
                    files = []
                    for root, _, filenames in os.walk(dir_path):
                        root_path = Path(root)
                        for filename in filenames:
                            files.append(root_path / filename)
            else:
                # List only the current directory
                files = list(dir_path.iterdir())
                if pattern:
                    # Filter by pattern
                    files = [f for f in files if f.match(pattern)]

            # Filter hidden files if requested
            if not include_hidden:
                files = [f for f in files if not f.name.startswith('.')]

            # Convert to relative paths
            relative_files = [str(f.relative_to(self.workspace_dir)) for f in files]

            self.history[-1]["count"] = len(relative_files)
            self.history[-1]["success"] = True
            return relative_files

        except (FileNotFoundError, NotADirectoryError):
            # Re-raise these specific exceptions
            self.history[-1]["success"] = False
            raise

        except Exception as e:
            self.history[-1]["error"] = str(e)
            self.history[-1]["success"] = False
            logger.error(f"Error listing files in {dir_path}: {e}")
            raise

    def search_files(self, pattern: str,
                    recursive: bool = True,
                    include_hidden: bool = False,
                    max_depth: Optional[int] = None) -> List[str]:
        """Search for files matching a pattern with enhanced options.

        Args:
            pattern: The glob pattern to search for.
            recursive: Whether to search recursively. Default is True.
            include_hidden: Whether to include hidden files. Default is False.
            max_depth: Maximum directory depth to search. Default is None (no limit).

        Returns:
            A list of files matching the pattern.
        """
        # Add to history
        self.history.append({
            "action": "search",
            "pattern": pattern,
            "recursive": recursive,
            "include_hidden": include_hidden,
            "max_depth": max_depth
        })

        try:
            # Resolve the pattern
            if not os.path.isabs(pattern):
                pattern = os.path.join(str(self.workspace_dir), pattern)

            # Search for files
            matches = glob.glob(pattern, recursive=recursive)

            # Convert to Path objects for easier filtering
            files = [Path(p) for p in matches]

            # Filter hidden files if requested
            if not include_hidden:
                files = [f for f in files if not any(part.startswith('.') for part in f.parts)]

            # Apply max depth filter if specified
            if max_depth is not None:
                base_depth = len(Path(self.workspace_dir).parts)
                files = [f for f in files if len(f.parts) - base_depth <= max_depth]

            # Convert to relative paths
            relative_files = [os.path.relpath(str(f), str(self.workspace_dir)) for f in files]

            self.history[-1]["count"] = len(relative_files)
            self.history[-1]["success"] = True
            return relative_files

        except Exception as e:
            self.history[-1]["error"] = str(e)
            self.history[-1]["success"] = False
            logger.error(f"Error searching for files with pattern {pattern}: {e}")
            return []

    def grep_files(self, pattern: str,
                  file_pattern: str = "*",
                  case_sensitive: bool = True,
                  whole_word: bool = False,
                  max_results: Optional[int] = None,
                  include_binary: bool = False) -> Dict[str, List[str]]:
        """Search for a pattern in files with enhanced options.

        Args:
            pattern: The pattern to search for.
            file_pattern: The pattern of files to search in. Default is "*".
            case_sensitive: Whether the search is case-sensitive. Default is True.
            whole_word: Whether to match whole words only. Default is False.
            max_results: Maximum number of results to return. Default is None (no limit).
            include_binary: Whether to search binary files. Default is False.

        Returns:
            A dictionary mapping file paths to lists of matching lines.
        """
        import re

        # Add to history
        self.history.append({
            "action": "grep",
            "pattern": pattern,
            "file_pattern": file_pattern,
            "case_sensitive": case_sensitive,
            "whole_word": whole_word,
            "max_results": max_results,
            "include_binary": include_binary
        })

        try:
            # Prepare the regex pattern
            if whole_word:
                pattern = r'\b' + pattern + r'\b'

            # Compile the pattern
            try:
                flags = 0 if case_sensitive else re.IGNORECASE
                regex = re.compile(pattern, flags)
            except re.error:
                # If the pattern is not a valid regex, use it as a literal string
                escaped_pattern = re.escape(pattern)
                if whole_word:
                    escaped_pattern = r'\b' + escaped_pattern + r'\b'
                regex = re.compile(escaped_pattern, flags)

            # Find files matching the file pattern
            files = self.search_files(file_pattern)

            # Search for the pattern in the files
            results = {}
            total_matches = 0

            for file in files:
                try:
                    # Skip binary files if requested
                    if not include_binary and self._is_binary_file(self._resolve_path(file)):
                        continue

                    # Read the file with caching disabled for large files
                    content = self.read_file(file, use_cache=False)
                    matches = []

                    # Process the file line by line
                    for i, line in enumerate(content.splitlines()):
                        if regex.search(line):
                            matches.append(f"{i+1}: {line}")
                            total_matches += 1

                            # Check if we've reached the maximum number of results
                            if max_results is not None and total_matches >= max_results:
                                if matches:
                                    results[file] = matches
                                self.history[-1]["truncated"] = True
                                self.history[-1]["success"] = True
                                self.history[-1]["match_count"] = total_matches
                                return results

                    if matches:
                        results[file] = matches

                except Exception as e:
                    # Log the error but continue with other files
                    logger.debug(f"Error searching in file {file}: {e}")

            self.history[-1]["success"] = True
            self.history[-1]["match_count"] = total_matches
            self.history[-1]["file_count"] = len(results)
            return results

        except Exception as e:
            self.history[-1]["error"] = str(e)
            self.history[-1]["success"] = False
            logger.error(f"Error searching for pattern {pattern}: {e}")
            return {}

    def _resolve_path(self, path: Union[str, Path]) -> Path:
        """Resolve a path relative to the workspace directory.

        Args:
            path: The path to resolve.

        Returns:
            The resolved path.
        """
        if isinstance(path, str):
            path = Path(path)

        if path.is_absolute():
            return path

        return self.workspace_dir / path

    def _get_file_hash(self, path: Union[str, Path]) -> str:
        """Get a hash of a file's contents.

        Args:
            path: The path to the file.

        Returns:
            A hash of the file's contents.
        """
        path = self._resolve_path(path)

        try:
            hasher = hashlib.md5()
            with open(path, "rb") as f:
                # Read in chunks to handle large files efficiently
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating hash for {path}: {e}")
            # Return a timestamp-based hash as fallback
            return f"timestamp-{os.path.getmtime(path)}"

    def _is_binary_file(self, path: Union[str, Path]) -> bool:
        """Check if a file is binary.

        Args:
            path: The path to the file.

        Returns:
            True if the file is binary, False otherwise.
        """
        path = self._resolve_path(path)

        # Check by extension first (faster)
        if path.suffix.lower() in self.BINARY_EXTENSIONS:
            return True

        # Check by MIME type
        mime_type, _ = mimetypes.guess_type(str(path))
        if mime_type and not mime_type.startswith(('text/', 'application/json', 'application/xml')):
            return True

        # As a last resort, check the content
        try:
            with open(path, 'rb') as f:
                # Read the first 8KB of the file
                chunk = f.read(8192)

            # Check for null bytes which usually indicate binary content
            if b'\x00' in chunk:
                return True

            # Count the ratio of printable ASCII characters
            printable_ratio = sum(32 <= byte <= 126 or byte in (9, 10, 13) for byte in chunk) / len(chunk) if chunk else 1

            # If less than 70% of the characters are printable ASCII, consider it binary
            return printable_ratio < 0.7

        except Exception as e:
            logger.error(f"Error checking if {path} is binary: {e}")
            # Default to False if we can't determine
            return False

    def get_file_info(self, path: Union[str, Path]) -> Dict[str, Any]:
        """Get detailed information about a file.

        Args:
            path: The path to the file.

        Returns:
            A dictionary with file information.
        """
        # Add to history
        self.history.append({"action": "get_info", "path": str(path)})

        # Resolve the path
        file_path = self._resolve_path(path)

        try:
            # Check if the file exists
            if not file_path.exists():
                error = FileNotFoundError(f"File not found: {file_path}")
                self.history[-1]["error"] = str(error)
                self.history[-1]["success"] = False
                raise error

            # Get basic file information
            stat = file_path.stat()

            info = {
                "name": file_path.name,
                "path": str(file_path),
                "relative_path": str(file_path.relative_to(self.workspace_dir)),
                "size": stat.st_size,
                "size_human": self._format_size(stat.st_size),
                "created": stat.st_ctime,
                "modified": stat.st_mtime,
                "accessed": stat.st_atime,
                "is_dir": file_path.is_dir(),
                "is_file": file_path.is_file(),
                "is_symlink": file_path.is_symlink(),
                "extension": file_path.suffix,
            }

            # Add additional information for files
            if file_path.is_file():
                info["is_binary"] = self._is_binary_file(file_path)

                # Get MIME type
                mime_type, encoding = mimetypes.guess_type(str(file_path))
                info["mime_type"] = mime_type
                info["encoding"] = encoding

                # Get hash
                info["hash"] = self._get_file_hash(file_path)

            # Add additional information for directories
            if file_path.is_dir():
                # Count files and subdirectories
                files = []
                dirs = []

                for item in file_path.iterdir():
                    if item.is_dir():
                        dirs.append(item.name)
                    else:
                        files.append(item.name)

                info["file_count"] = len(files)
                info["dir_count"] = len(dirs)
                info["contents"] = sorted(dirs) + sorted(files)

            self.history[-1]["success"] = True
            return info

        except FileNotFoundError:
            # Re-raise these specific exceptions
            self.history[-1]["success"] = False
            raise

        except Exception as e:
            self.history[-1]["error"] = str(e)
            self.history[-1]["success"] = False
            logger.error(f"Error getting info for {file_path}: {e}")
            raise

    def _format_size(self, size_bytes: int) -> str:
        """Format a size in bytes to a human-readable string.

        Args:
            size_bytes: The size in bytes.

        Returns:
            A human-readable string.
        """
        if size_bytes < 1024:
            return f"{size_bytes} B"

        for unit in ['KB', 'MB', 'GB', 'TB']:
            size_bytes /= 1024
            if size_bytes < 1024:
                return f"{size_bytes:.2f} {unit}"

        return f"{size_bytes:.2f} PB"

    def get_history(self) -> List[Dict[str, Any]]:
        """Get the file operation history.

        Returns:
            The file operation history.
        """
        return self.history

    def clear_cache(self) -> None:
        """Clear the file cache."""
        self._file_cache.clear()
        logger.info("File cache cleared")

    def create_directory(self, path: Union[str, Path], parents: bool = True, exist_ok: bool = True) -> Dict[str, Any]:
        """Create a directory with nested folder support.

        Args:
            path: The path to the directory to create.
            parents: Whether to create parent directories if they don't exist.
            exist_ok: Whether to raise an error if the directory already exists.

        Returns:
            Result dictionary with success status and details.
        """
        try:
            self.history.append({"action": "create_directory", "path": str(path)})

            dir_path = self._resolve_path(path)

            # Create the directory
            dir_path.mkdir(parents=parents, exist_ok=exist_ok)

            result = {
                "success": True,
                "message": f"Directory created successfully: {path}",
                "path": str(dir_path),
                "exists": dir_path.exists(),
                "is_directory": dir_path.is_dir()
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except FileExistsError as e:
            error_msg = f"Directory already exists: {path}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            return {
                "success": False,
                "error": error_msg,
                "path": str(path)
            }
        except Exception as e:
            error_msg = f"Failed to create directory: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error creating directory {path}: {e}")
            return {
                "success": False,
                "error": error_msg,
                "path": str(path)
            }

    def edit_file(self, path: Union[str, Path], content: str, line_number: Optional[int] = None,
                  operation: str = "replace", encoding: str = "utf-8") -> Dict[str, Any]:
        """Edit a file with various operations (insert, delete, modify lines or sections).

        Args:
            path: The path to the file to edit.
            content: The content to insert/replace.
            line_number: The line number for the operation (1-based).
            operation: The operation to perform ('replace', 'insert', 'delete', 'append').
            encoding: The encoding to use.

        Returns:
            Result dictionary with success status and details.
        """
        try:
            self.history.append({
                "action": "edit_file",
                "path": str(path),
                "operation": operation,
                "line_number": line_number
            })

            file_path = self._resolve_path(path)

            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"File not found: {path}",
                    "path": str(file_path)
                }

            # Read current content
            current_content = self.read_file(path, use_cache=False)
            lines = current_content.split('\n')

            # Perform the operation
            if operation == "replace":
                if line_number is None:
                    # Replace entire file
                    new_content = content
                else:
                    # Replace specific line
                    if 1 <= line_number <= len(lines):
                        lines[line_number - 1] = content
                        new_content = '\n'.join(lines)
                    else:
                        return {
                            "success": False,
                            "error": f"Line number {line_number} out of range (1-{len(lines)})",
                            "path": str(file_path)
                        }

            elif operation == "insert":
                if line_number is None:
                    line_number = len(lines) + 1

                if 1 <= line_number <= len(lines) + 1:
                    lines.insert(line_number - 1, content)
                    new_content = '\n'.join(lines)
                else:
                    return {
                        "success": False,
                        "error": f"Line number {line_number} out of range (1-{len(lines) + 1})",
                        "path": str(file_path)
                    }

            elif operation == "delete":
                if line_number is None:
                    return {
                        "success": False,
                        "error": "Line number required for delete operation",
                        "path": str(file_path)
                    }

                if 1 <= line_number <= len(lines):
                    del lines[line_number - 1]
                    new_content = '\n'.join(lines)
                else:
                    return {
                        "success": False,
                        "error": f"Line number {line_number} out of range (1-{len(lines)})",
                        "path": str(file_path)
                    }

            elif operation == "append":
                lines.append(content)
                new_content = '\n'.join(lines)

            else:
                return {
                    "success": False,
                    "error": f"Unknown operation: {operation}",
                    "path": str(file_path)
                }

            # Write the modified content
            success = self.write_file(path, new_content, encoding)

            result = {
                "success": success,
                "message": f"File edited successfully with {operation} operation",
                "path": str(file_path),
                "operation": operation,
                "line_number": line_number,
                "lines_before": len(lines) if operation != "replace" or line_number else len(current_content.split('\n')),
                "lines_after": len(new_content.split('\n'))
            }

            self.history[-1]["success"] = success
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to edit file: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error editing file {path}: {e}")
            return {
                "success": False,
                "error": error_msg,
                "path": str(path)
            }

    def insert_edit_into_file(self, path: Union[str, Path], content: str,
                             line_number: int, column: int = 0) -> Dict[str, Any]:
        """Insert code/content at a specific location in a file.

        Args:
            path: The path to the file to edit.
            content: The content to insert.
            line_number: The line number to insert at (1-based).
            column: The column position to insert at (0-based).

        Returns:
            Result dictionary with success status and details.
        """
        try:
            self.history.append({
                "action": "insert_edit_into_file",
                "path": str(path),
                "line_number": line_number,
                "column": column
            })

            file_path = self._resolve_path(path)

            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"File not found: {path}",
                    "path": str(file_path)
                }

            # Read current content
            current_content = self.read_file(path, use_cache=False)
            lines = current_content.split('\n')

            if not (1 <= line_number <= len(lines)):
                return {
                    "success": False,
                    "error": f"Line number {line_number} out of range (1-{len(lines)})",
                    "path": str(file_path)
                }

            # Get the target line
            target_line = lines[line_number - 1]

            # Insert content at the specified column
            if column > len(target_line):
                column = len(target_line)

            new_line = target_line[:column] + content + target_line[column:]
            lines[line_number - 1] = new_line

            # Write the modified content
            new_content = '\n'.join(lines)
            success = self.write_file(path, new_content)

            result = {
                "success": success,
                "message": f"Content inserted successfully at line {line_number}, column {column}",
                "path": str(file_path),
                "line_number": line_number,
                "column": column,
                "inserted_content": content
            }

            self.history[-1]["success"] = success
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to insert content: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error inserting content into file {path}: {e}")
            return {
                "success": False,
                "error": error_msg,
                "path": str(path)
            }

    def file_search(self, pattern: str, search_type: str = "glob",
                   recursive: bool = True, max_results: int = 100) -> Dict[str, Any]:
        """Search files by glob patterns or regex.

        Args:
            pattern: The search pattern.
            search_type: The type of search ('glob', 'regex', 'name').
            recursive: Whether to search recursively.
            max_results: Maximum number of results to return.

        Returns:
            Result dictionary with found files and details.
        """
        try:
            self.history.append({
                "action": "file_search",
                "pattern": pattern,
                "search_type": search_type,
                "recursive": recursive
            })

            found_files = []
            search_path = self.workspace_dir

            if search_type == "glob":
                if recursive:
                    pattern_path = f"**/{pattern}"
                    found_files = list(search_path.glob(pattern_path))
                else:
                    found_files = list(search_path.glob(pattern))

            elif search_type == "regex":
                import re
                regex_pattern = re.compile(pattern)

                if recursive:
                    for file_path in search_path.rglob("*"):
                        if file_path.is_file() and regex_pattern.search(file_path.name):
                            found_files.append(file_path)
                            if len(found_files) >= max_results:
                                break
                else:
                    for file_path in search_path.iterdir():
                        if file_path.is_file() and regex_pattern.search(file_path.name):
                            found_files.append(file_path)
                            if len(found_files) >= max_results:
                                break

            elif search_type == "name":
                if recursive:
                    for file_path in search_path.rglob("*"):
                        if file_path.is_file() and pattern.lower() in file_path.name.lower():
                            found_files.append(file_path)
                            if len(found_files) >= max_results:
                                break
                else:
                    for file_path in search_path.iterdir():
                        if file_path.is_file() and pattern.lower() in file_path.name.lower():
                            found_files.append(file_path)
                            if len(found_files) >= max_results:
                                break

            # Convert to relative paths and get file info
            results = []
            for file_path in found_files[:max_results]:
                try:
                    relative_path = file_path.relative_to(self.workspace_dir)
                    file_info = {
                        "path": str(relative_path),
                        "absolute_path": str(file_path),
                        "size": file_path.stat().st_size,
                        "modified": file_path.stat().st_mtime,
                        "is_binary": self._is_binary_file(file_path)
                    }
                    results.append(file_info)
                except Exception as e:
                    logger.warning(f"Error getting info for {file_path}: {e}")
                    continue

            result = {
                "success": True,
                "pattern": pattern,
                "search_type": search_type,
                "recursive": recursive,
                "total_found": len(results),
                "files": results
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to search files: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error searching files with pattern {pattern}: {e}")
            return {
                "success": False,
                "error": error_msg,
                "pattern": pattern
            }

    def list_code_usages(self, symbol: str, file_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Find where symbols (functions/classes) are used across the codebase.

        Args:
            symbol: The symbol to search for (function name, class name, etc.).
            file_types: List of file extensions to search in (e.g., ['.py', '.js']).

        Returns:
            Result dictionary with usage locations and details.
        """
        try:
            self.history.append({
                "action": "list_code_usages",
                "symbol": symbol,
                "file_types": file_types
            })

            if file_types is None:
                file_types = ['.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h']

            usages = []
            search_path = self.workspace_dir

            # Search for the symbol in code files
            for file_path in search_path.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in file_types:
                    try:
                        if self._is_binary_file(file_path):
                            continue

                        content = self.read_file(file_path, use_cache=True)
                        lines = content.split('\n')

                        for line_num, line in enumerate(lines, 1):
                            if symbol in line:
                                # Try to determine the context (definition vs usage)
                                context = "usage"
                                if any(keyword in line for keyword in ["def ", "class ", "function ", "const ", "let ", "var "]):
                                    if symbol in line:
                                        context = "definition"

                                relative_path = file_path.relative_to(self.workspace_dir)
                                usages.append({
                                    "file": str(relative_path),
                                    "line": line_num,
                                    "content": line.strip(),
                                    "context": context,
                                    "column": line.find(symbol) + 1
                                })

                    except Exception as e:
                        logger.warning(f"Error searching in {file_path}: {e}")
                        continue

            result = {
                "success": True,
                "symbol": symbol,
                "file_types": file_types,
                "total_usages": len(usages),
                "usages": usages
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to find code usages: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error finding usages for symbol {symbol}: {e}")
            return {
                "success": False,
                "error": error_msg,
                "symbol": symbol
            }

    def get_changed_files(self, since: Optional[str] = None) -> Dict[str, Any]:
        """Show Git diffs of modified files.

        Args:
            since: Git reference to compare against (e.g., 'HEAD~1', 'main').

        Returns:
            Result dictionary with changed files and their diffs.
        """
        try:
            self.history.append({
                "action": "get_changed_files",
                "since": since
            })

            import subprocess

            # Check if we're in a git repository
            try:
                subprocess.run(["git", "rev-parse", "--git-dir"],
                             cwd=self.workspace_dir, check=True,
                             capture_output=True, text=True)
            except subprocess.CalledProcessError:
                return {
                    "success": False,
                    "error": "Not in a Git repository",
                    "path": str(self.workspace_dir)
                }

            changed_files = []

            # Get list of changed files
            if since:
                cmd = ["git", "diff", "--name-status", since]
            else:
                cmd = ["git", "status", "--porcelain"]

            result = subprocess.run(cmd, cwd=self.workspace_dir,
                                  capture_output=True, text=True, check=True)

            for line in result.stdout.strip().split('\n'):
                if not line:
                    continue

                if since:
                    # Git diff format: M\tfilename
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        status = parts[0]
                        filename = parts[1]
                    else:
                        continue
                else:
                    # Git status format: MM filename
                    if len(line) >= 3:
                        status = line[:2].strip()
                        filename = line[3:]
                    else:
                        continue

                # Get the diff for this file
                try:
                    if since:
                        diff_cmd = ["git", "diff", since, "--", filename]
                    else:
                        diff_cmd = ["git", "diff", "HEAD", "--", filename]

                    diff_result = subprocess.run(diff_cmd, cwd=self.workspace_dir,
                                               capture_output=True, text=True)

                    file_info = {
                        "filename": filename,
                        "status": status,
                        "diff": diff_result.stdout if diff_result.returncode == 0 else None
                    }

                    # Get file stats if file exists
                    file_path = self.workspace_dir / filename
                    if file_path.exists():
                        stat = file_path.stat()
                        file_info.update({
                            "size": stat.st_size,
                            "modified": stat.st_mtime,
                            "is_binary": self._is_binary_file(file_path)
                        })

                    changed_files.append(file_info)

                except Exception as e:
                    logger.warning(f"Error getting diff for {filename}: {e}")
                    changed_files.append({
                        "filename": filename,
                        "status": status,
                        "error": str(e)
                    })

            result = {
                "success": True,
                "since": since,
                "total_changed": len(changed_files),
                "files": changed_files
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except subprocess.CalledProcessError as e:
            error_msg = f"Git command failed: {e.stderr if e.stderr else str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            return {
                "success": False,
                "error": error_msg,
                "since": since
            }
        except Exception as e:
            error_msg = f"Failed to get changed files: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error getting changed files: {e}")
            return {
                "success": False,
                "error": error_msg,
                "since": since
            }

    def get_errors(self, file_path: Optional[str] = None, error_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Fetch lint, syntax, or compiler errors from files.

        Args:
            file_path: Specific file to check (if None, checks all relevant files).
            error_types: Types of errors to check ('syntax', 'lint', 'type').

        Returns:
            Result dictionary with errors found.
        """
        try:
            self.history.append({
                "action": "get_errors",
                "file_path": file_path,
                "error_types": error_types
            })

            if error_types is None:
                error_types = ['syntax', 'lint']

            errors = []
            files_to_check = []

            if file_path:
                files_to_check = [self._resolve_path(file_path)]
            else:
                # Check all code files
                for ext in ['.py', '.js', '.ts', '.jsx', '.tsx']:
                    files_to_check.extend(self.workspace_dir.rglob(f"*{ext}"))

            for file_path_obj in files_to_check:
                if not file_path_obj.exists() or not file_path_obj.is_file():
                    continue

                file_errors = []
                relative_path = file_path_obj.relative_to(self.workspace_dir)

                # Check syntax errors
                if 'syntax' in error_types:
                    syntax_errors = self._check_syntax_errors(file_path_obj)
                    file_errors.extend(syntax_errors)

                # Check lint errors (basic implementation)
                if 'lint' in error_types:
                    lint_errors = self._check_lint_errors(file_path_obj)
                    file_errors.extend(lint_errors)

                if file_errors:
                    errors.append({
                        "file": str(relative_path),
                        "errors": file_errors
                    })

            result = {
                "success": True,
                "file_path": file_path,
                "error_types": error_types,
                "total_files_checked": len(files_to_check),
                "files_with_errors": len(errors),
                "errors": errors
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to get errors: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error getting errors: {e}")
            return {
                "success": False,
                "error": error_msg,
                "file_path": file_path
            }

    def _check_syntax_errors(self, file_path: Path) -> List[Dict[str, Any]]:
        """Check for syntax errors in a file."""
        errors = []

        try:
            if file_path.suffix == '.py':
                # Check Python syntax
                content = self.read_file(file_path, use_cache=True)
                try:
                    ast.parse(content)
                except SyntaxError as e:
                    errors.append({
                        "type": "syntax",
                        "line": e.lineno,
                        "column": e.offset,
                        "message": e.msg,
                        "severity": "error"
                    })

            # Add more language-specific syntax checking here

        except Exception as e:
            logger.warning(f"Error checking syntax for {file_path}: {e}")

        return errors

    def _check_lint_errors(self, file_path: Path) -> List[Dict[str, Any]]:
        """Check for basic lint errors in a file."""
        errors = []

        try:
            content = self.read_file(file_path, use_cache=True)
            lines = content.split('\n')

            for line_num, line in enumerate(lines, 1):
                # Basic lint checks
                if len(line) > 120:
                    errors.append({
                        "type": "lint",
                        "line": line_num,
                        "column": 121,
                        "message": "Line too long (>120 characters)",
                        "severity": "warning"
                    })

                if line.rstrip() != line:
                    errors.append({
                        "type": "lint",
                        "line": line_num,
                        "column": len(line.rstrip()) + 1,
                        "message": "Trailing whitespace",
                        "severity": "warning"
                    })

        except Exception as e:
            logger.warning(f"Error checking lint for {file_path}: {e}")

        return errors

    def semantic_search(self, query: str, file_types: Optional[List[str]] = None,
                       max_results: int = 50) -> Dict[str, Any]:
        """Natural language search across codebase.

        Args:
            query: Natural language search query.
            file_types: File types to search in.
            max_results: Maximum number of results.

        Returns:
            Result dictionary with semantic search results.
        """
        try:
            self.history.append({
                "action": "semantic_search",
                "query": query,
                "file_types": file_types
            })

            if file_types is None:
                file_types = list(self.code_extensions)

            results = []
            search_terms = query.lower().split()

            # Search through code files
            for file_path in self.workspace_dir.rglob("*"):
                if (file_path.is_file() and
                    file_path.suffix.lower() in file_types and
                    not self._is_binary_file(file_path)):

                    try:
                        content = self.read_file(file_path, use_cache=True)
                        content_lower = content.lower()

                        # Calculate relevance score
                        score = 0
                        matches = []

                        for term in search_terms:
                            term_count = content_lower.count(term)
                            if term_count > 0:
                                score += term_count
                                matches.append(term)

                        if score > 0:
                            # Find relevant lines
                            lines = content.split('\n')
                            relevant_lines = []

                            for line_num, line in enumerate(lines, 1):
                                line_lower = line.lower()
                                if any(term in line_lower for term in search_terms):
                                    relevant_lines.append({
                                        "line": line_num,
                                        "content": line.strip(),
                                        "relevance": sum(1 for term in search_terms if term in line_lower)
                                    })

                            relative_path = file_path.relative_to(self.workspace_dir)
                            results.append({
                                "file": str(relative_path),
                                "score": score,
                                "matches": matches,
                                "relevant_lines": sorted(relevant_lines,
                                                       key=lambda x: x["relevance"],
                                                       reverse=True)[:10]
                            })

                    except Exception as e:
                        logger.warning(f"Error searching in {file_path}: {e}")
                        continue

            # Sort by relevance score
            results.sort(key=lambda x: x["score"], reverse=True)
            results = results[:max_results]

            result = {
                "success": True,
                "query": query,
                "file_types": file_types,
                "total_results": len(results),
                "results": results
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to perform semantic search: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error in semantic search: {e}")
            return {
                "success": False,
                "error": error_msg,
                "query": query
            }

    def get_project_setup_info(self) -> Dict[str, Any]:
        """Detect framework, language, tooling, etc. in the current project.

        Returns:
            Result dictionary with project setup information.
        """
        try:
            self.history.append({"action": "get_project_setup_info"})

            project_info = {
                "languages": [],
                "frameworks": [],
                "build_tools": [],
                "package_managers": [],
                "config_files": [],
                "dependencies": {},
                "project_type": "unknown",
                "entry_points": []
            }

            # Check for common configuration files
            config_files = {
                "package.json": "Node.js/JavaScript",
                "requirements.txt": "Python",
                "Pipfile": "Python (Pipenv)",
                "pyproject.toml": "Python (Modern)",
                "setup.py": "Python",
                "Cargo.toml": "Rust",
                "go.mod": "Go",
                "pom.xml": "Java (Maven)",
                "build.gradle": "Java/Kotlin (Gradle)",
                "composer.json": "PHP",
                "Gemfile": "Ruby",
                "CMakeLists.txt": "C/C++ (CMake)",
                "Makefile": "C/C++ (Make)",
                "tsconfig.json": "TypeScript",
                "angular.json": "Angular",
                "vue.config.js": "Vue.js",
                "next.config.js": "Next.js",
                "nuxt.config.js": "Nuxt.js",
                "webpack.config.js": "Webpack",
                "vite.config.js": "Vite",
                "rollup.config.js": "Rollup",
                "babel.config.js": "Babel",
                "eslint.config.js": "ESLint",
                ".eslintrc.js": "ESLint",
                "prettier.config.js": "Prettier",
                "jest.config.js": "Jest",
                "cypress.json": "Cypress",
                "docker-compose.yml": "Docker Compose",
                "Dockerfile": "Docker",
                "kubernetes.yaml": "Kubernetes",
                "terraform.tf": "Terraform"
            }

            # Check for config files
            for config_file, description in config_files.items():
                config_path = self.workspace_dir / config_file
                if config_path.exists():
                    project_info["config_files"].append({
                        "file": config_file,
                        "description": description,
                        "path": str(config_path)
                    })

            # Analyze file extensions to determine languages
            language_extensions = {
                ".py": "Python",
                ".js": "JavaScript",
                ".ts": "TypeScript",
                ".jsx": "React (JSX)",
                ".tsx": "React (TSX)",
                ".vue": "Vue.js",
                ".java": "Java",
                ".kt": "Kotlin",
                ".scala": "Scala",
                ".go": "Go",
                ".rs": "Rust",
                ".cpp": "C++",
                ".c": "C",
                ".h": "C/C++ Header",
                ".cs": "C#",
                ".php": "PHP",
                ".rb": "Ruby",
                ".swift": "Swift",
                ".m": "Objective-C",
                ".r": "R",
                ".sql": "SQL",
                ".html": "HTML",
                ".css": "CSS",
                ".scss": "SCSS",
                ".sass": "Sass",
                ".less": "Less"
            }

            language_counts = {}
            for file_path in self.workspace_dir.rglob("*"):
                if file_path.is_file():
                    ext = file_path.suffix.lower()
                    if ext in language_extensions:
                        lang = language_extensions[ext]
                        language_counts[lang] = language_counts.get(lang, 0) + 1

            # Sort languages by file count
            project_info["languages"] = [
                {"language": lang, "file_count": count}
                for lang, count in sorted(language_counts.items(),
                                        key=lambda x: x[1], reverse=True)
            ]

            # Determine project type based on config files and languages
            if any("package.json" in cf["file"] for cf in project_info["config_files"]):
                project_info["project_type"] = "Node.js/JavaScript"
                project_info["package_managers"].append("npm")

                # Check for specific frameworks
                package_json_path = self.workspace_dir / "package.json"
                if package_json_path.exists():
                    try:
                        import json
                        with open(package_json_path, 'r') as f:
                            package_data = json.load(f)

                        dependencies = {**package_data.get("dependencies", {}),
                                      **package_data.get("devDependencies", {})}
                        project_info["dependencies"]["npm"] = dependencies

                        # Detect frameworks
                        if "react" in dependencies:
                            project_info["frameworks"].append("React")
                        if "vue" in dependencies:
                            project_info["frameworks"].append("Vue.js")
                        if "angular" in dependencies or "@angular/core" in dependencies:
                            project_info["frameworks"].append("Angular")
                        if "next" in dependencies:
                            project_info["frameworks"].append("Next.js")
                        if "nuxt" in dependencies:
                            project_info["frameworks"].append("Nuxt.js")
                        if "express" in dependencies:
                            project_info["frameworks"].append("Express.js")
                        if "fastify" in dependencies:
                            project_info["frameworks"].append("Fastify")

                        # Entry points
                        if "main" in package_data:
                            project_info["entry_points"].append(package_data["main"])
                        if "scripts" in package_data and "start" in package_data["scripts"]:
                            project_info["entry_points"].append("npm start")

                    except Exception as e:
                        logger.warning(f"Error parsing package.json: {e}")

            elif any("requirements.txt" in cf["file"] or "setup.py" in cf["file"] or "pyproject.toml" in cf["file"]
                    for cf in project_info["config_files"]):
                project_info["project_type"] = "Python"
                project_info["package_managers"].append("pip")

                # Check for Python frameworks
                python_files = list(self.workspace_dir.rglob("*.py"))
                if python_files:
                    # Sample some Python files to detect frameworks
                    for py_file in python_files[:10]:  # Check first 10 files
                        try:
                            content = self.read_file(py_file, use_cache=True)
                            if "from django" in content or "import django" in content:
                                project_info["frameworks"].append("Django")
                            if "from flask" in content or "import flask" in content:
                                project_info["frameworks"].append("Flask")
                            if "from fastapi" in content or "import fastapi" in content:
                                project_info["frameworks"].append("FastAPI")
                            if "streamlit" in content:
                                project_info["frameworks"].append("Streamlit")
                        except Exception:
                            continue

                # Check for main.py or app.py
                for entry_file in ["main.py", "app.py", "run.py", "server.py"]:
                    if (self.workspace_dir / entry_file).exists():
                        project_info["entry_points"].append(entry_file)

            # Add more project type detection logic here for other languages

            result = {
                "success": True,
                "project_info": project_info,
                "workspace_path": str(self.workspace_dir)
            }

            self.history[-1]["success"] = True
            self.history[-1]["result"] = result
            return result

        except Exception as e:
            error_msg = f"Failed to get project setup info: {str(e)}"
            self.history[-1]["error"] = error_msg
            self.history[-1]["success"] = False
            logger.error(f"Error getting project setup info: {e}")
            return {
                "success": False,
                "error": error_msg
            }
