#!/usr/bin/env python3
"""
Test script to verify that the tools work correctly.
"""

import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from agent import Agent
from models import ModelManager
from conversation import ConversationManager

def test_tools():
    """Test the tools directly."""
    print("Testing AI Agent Tools...")

    try:
        # Initialize required managers
        model_manager = ModelManager()
        conversation_manager = ConversationManager(history_dir=Path.cwd() / "conversations")

        # Initialize the agent
        agent = Agent(
            model_manager=model_manager,
            conversation_manager=conversation_manager,
            workspace_dir=Path.cwd()
        )
        print("✓ Agent initialized successfully")
        
        # Test status tool
        print("\n--- Testing status tool ---")
        status_result = agent.get_system_status("")
        print(f"Status result: {status_result[:200]}...")
        
        # Test file tool
        print("\n--- Testing file tool ---")
        file_result = agent._execute_file("list .")
        print(f"File list result: {file_result[:200]}...")
        
        # Test shell tool
        print("\n--- Testing shell tool ---")
        shell_result = agent._execute_shell("run_in_terminal ls")
        print(f"Shell result: {shell_result[:200]}...")
        
        # Test analyze tool
        print("\n--- Testing analyze tool ---")
        analyze_result = agent.analyze_and_optimize("def test(): pass")
        print(f"Analyze result: {analyze_result[:200]}...")

        # Test web tool
        print("\n--- Testing web tool ---")
        web_result = agent._execute_web("search python tutorial")
        print(f"Web result: {web_result[:200]}...")

        # Test AI reasoning tool
        print("\n--- Testing AI reasoning tool ---")
        ai_result = agent._execute_ai_reasoning("intent_recognition 'I want to create a function'")
        print(f"AI reasoning result: {ai_result[:200]}...")

        # Test workflow tool
        print("\n--- Testing workflow tool ---")
        workflow_result = agent._execute_workflow("plan_next_step 'Starting a Python project' 'Build a web application'")
        print(f"Workflow result: {workflow_result[:200]}...")

        # Test advanced execution tool
        print("\n--- Testing advanced execution tool ---")
        exec_result = agent._execute_advanced_execution("result_analysis test_id comprehensive")
        print(f"Execution result: {exec_result[:200]}...")

        print("\n✓ All major tool categories tested successfully!")
        
    except Exception as e:
        print(f"✗ Error testing tools: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tools()
