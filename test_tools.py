#!/usr/bin/env python3
"""
Test script to verify that the tools work correctly.
"""

import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from agent import Agent

def test_tools():
    """Test the tools directly."""
    print("Testing AI Agent Tools...")
    
    try:
        # Initialize the agent
        agent = Agent(workspace_dir=Path.cwd())
        print("✓ Agent initialized successfully")
        
        # Test status tool
        print("\n--- Testing status tool ---")
        status_result = agent.get_system_status("")
        print(f"Status result: {status_result[:200]}...")
        
        # Test file tool
        print("\n--- Testing file tool ---")
        file_result = agent._execute_file("list_dir .")
        print(f"File list result: {file_result[:200]}...")
        
        # Test shell tool
        print("\n--- Testing shell tool ---")
        shell_result = agent._execute_shell("run_in_terminal ls")
        print(f"Shell result: {shell_result[:200]}...")
        
        # Test analyze tool
        print("\n--- Testing analyze tool ---")
        analyze_result = agent.analyze_and_optimize("def test(): pass")
        print(f"Analyze result: {analyze_result[:200]}...")
        
        print("\n✓ All tools tested successfully!")
        
    except Exception as e:
        print(f"✗ Error testing tools: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tools()
