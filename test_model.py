#!/usr/bin/env python3
"""
Test script to verify that the model manager works correctly.
"""

import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from models import ModelManager

def test_model():
    """Test the model manager directly."""
    print("Testing Model Manager...")
    
    try:
        # Initialize the model manager
        model_manager = ModelManager()
        print("✓ Model manager initialized successfully")
        print(f"Provider: {model_manager.provider}")
        print(f"Model: {model_manager.model_name}")
        
        # Test a simple generation
        print("\n--- Testing simple generation ---")
        response = model_manager.generate(
            prompt="Say hello in one word",
            system_prompt="You are a helpful assistant.",
            conversation_history=[]
        )
        print(f"Response: {response}")
        
        print("\n✓ Model manager tested successfully!")
        
    except Exception as e:
        print(f"✗ Error testing model manager: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_model()
